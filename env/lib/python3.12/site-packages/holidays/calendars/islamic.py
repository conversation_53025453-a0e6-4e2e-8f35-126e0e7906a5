#  holidays
#  --------
#  A fast, efficient Python library for generating country, province and state
#  specific sets of holidays on the fly. It aims to make determining whether a
#  specific date is a holiday as fast and flexible as possible.
#
#  Authors: <AUTHORS>
# <AUTHOR> <EMAIL> (c) 2017-2023
# <AUTHOR> <EMAIL> (c) 2014-2017
#  Website: https://github.com/vacanza/holidays
#  License: MIT (see LICENSE file)

from collections.abc import Iterable
from datetime import date

from holidays.calendars.custom import _CustomCalendar
from holidays.calendars.gregorian import JAN, FEB, MAR, APR, MAY, JUN, JUL, AUG, SEP, OCT, NOV, DEC
from holidays.helpers import _normalize_tuple

ALI_AL_RIDA_DEATH = "ALI_AL_RIDA_DEATH"
ALI_BIRTHDAY = "ALI_BIRTHDAY"
ALI_DEATH = "ALI_DEATH"
ARBAEEN = "ARBAEEN"
ASHURA = "ASHURA"
EID_AL_ADHA = "EID_AL_ADHA"
EID_AL_FITR = "EID_AL_FITR"
EID_AL_GHADIR = "EID_AL_GHADIR"
FATIMA_DEATH = "FATIMA_DEATH"
GRAND_MAGAL_OF_TOUBA = "GRAND_MAGAL_OF_TOUBA"
HARI_HOL_JOHOR = "HARI_HOL_JOHOR"
HASAN_AL_ASKARI_DEATH = "HASAN_AL_ASKARI_DEATH"
HIJRI_NEW_YEAR = "HIJRI_NEW_YEAR"
IMAM_MAHDI_BIRTHDAY = "IMAM_MAHDI_BIRTHDAY"
ISRA_AND_MIRAJ = "ISRA_AND_MIRAJ"
LAYLAT_AL_QADR = "LAYLAT_AL_QADR"
MALDIVES_EMBRACED_ISLAM_DAY = "MALDIVES_EMBRACED_ISLAM_DAY"
MAWLID = "MAWLID"
NUZUL_AL_QURAN = "NUZUL_AL_QURAN"
PROPHET_DEATH = "PROPHET_DEATH"
QUAMEE_DHUVAS = "QUAMEE_DHUVAS"
RAMADAN_BEGINNING = "RAMADAN_BEGINNING"
SADIQ_BIRTHDAY = "SADIQ_BIRTHDAY"
SADIQ_DEATH = "SADIQ_DEATH"
TASUA = "TASUA"


class _IslamicLunar:
    ALI_AL_RIDA_DEATH_DATES = {
        1924: (SEP, 28),
        1925: (SEP, 18),
        1926: (SEP, 7),
        1927: (AUG, 27),
        1928: (AUG, 15),
        1929: (AUG, 5),
        1930: (JUL, 25),
        1931: (JUL, 16),
        1932: (JUL, 4),
        1933: (JUN, 23),
        1934: (JUN, 12),
        1935: (JUN, 2),
        1936: (MAY, 21),
        1937: (MAY, 10),
        1938: (APR, 29),
        1939: (APR, 20),
        1940: (APR, 8),
        1941: (MAR, 27),
        1942: (MAR, 17),
        1943: (MAR, 6),
        1944: (FEB, 23),
        1945: (FEB, 12),
        1946: (FEB, 1),
        1947: (JAN, 22),
        1948: ((JAN, 11), (DEC, 30)),
        1949: (DEC, 20),
        1950: (DEC, 10),
        1951: (NOV, 29),
        1952: (NOV, 18),
        1953: (NOV, 7),
        1954: (OCT, 27),
        1955: (OCT, 17),
        1956: (OCT, 5),
        1957: (SEP, 24),
        1958: (SEP, 14),
        1959: (SEP, 3),
        1960: (AUG, 22),
        1961: (AUG, 11),
        1962: (JUL, 31),
        1963: (JUL, 21),
        1964: (JUL, 9),
        1965: (JUN, 28),
        1966: (JUN, 19),
        1967: (JUN, 7),
        1968: (MAY, 27),
        1969: (MAY, 16),
        1970: (MAY, 6),
        1971: (APR, 25),
        1972: (APR, 13),
        1973: (APR, 3),
        1974: (MAR, 23),
        1975: (MAR, 12),
        1976: (FEB, 29),
        1977: (FEB, 18),
        1978: (FEB, 7),
        1979: (JAN, 28),
        1980: (JAN, 18),
        1981: ((JAN, 6), (DEC, 26)),
        1982: (DEC, 15),
        1983: (DEC, 4),
        1984: (NOV, 22),
        1985: (NOV, 12),
        1986: (NOV, 2),
        1987: (OCT, 22),
        1988: (OCT, 10),
        1989: (SEP, 29),
        1990: (SEP, 19),
        1991: (SEP, 8),
        1992: (AUG, 28),
        1993: (AUG, 17),
        1994: (AUG, 7),
        1995: (JUL, 27),
        1996: (JUL, 15),
        1997: (JUL, 4),
        1998: (JUN, 24),
        1999: (JUN, 14),
        2000: (JUN, 2),
        2001: (MAY, 23),
        2002: (MAY, 12),
        2003: (MAY, 1),
        2004: (APR, 19),
        2005: (APR, 9),
        2006: (MAR, 29),
        2007: (MAR, 19),
        2008: (MAR, 8),
        2009: (FEB, 25),
        2010: (FEB, 14),
        2011: (FEB, 3),
        2012: (JAN, 23),
        2013: (JAN, 12),
        2014: ((JAN, 1), (DEC, 22)),
        2015: (DEC, 11),
        2016: (NOV, 29),
        2017: (NOV, 18),
        2018: (NOV, 8),
        2019: (OCT, 28),
        2020: (OCT, 17),
        2021: (OCT, 6),
        2022: (SEP, 26),
        2023: (SEP, 15),
        2024: (SEP, 3),
        2025: (AUG, 23),
        2026: (AUG, 13),
        2027: (AUG, 2),
        2028: (JUL, 22),
        2029: (JUL, 12),
        2030: (JUL, 1),
        2031: (JUN, 20),
        2032: (JUN, 8),
        2033: (MAY, 28),
        2034: (MAY, 18),
        2035: (MAY, 8),
        2036: (APR, 26),
        2037: (APR, 16),
        2038: (APR, 5),
        2039: (MAR, 25),
        2040: (MAR, 13),
        2041: (MAR, 3),
        2042: (FEB, 20),
        2043: (FEB, 10),
        2044: (JAN, 30),
        2045: (JAN, 18),
        2046: ((JAN, 7), (DEC, 27)),
        2047: (DEC, 17),
        2048: (DEC, 6),
        2049: (NOV, 25),
        2050: (NOV, 14),
        2051: (NOV, 4),
        2052: (OCT, 23),
        2053: (OCT, 12),
        2054: (OCT, 1),
        2055: (SEP, 21),
        2056: (SEP, 10),
        2057: (AUG, 30),
        2058: (AUG, 19),
        2059: (AUG, 8),
        2060: (JUL, 27),
        2061: (JUL, 17),
        2062: (JUL, 7),
        2063: (JUN, 26),
        2064: (JUN, 15),
        2065: (JUN, 4),
        2066: (MAY, 24),
        2067: (MAY, 13),
        2068: (MAY, 2),
        2069: (APR, 21),
        2070: (APR, 11),
        2071: (APR, 1),
        2072: (MAR, 20),
        2073: (MAR, 9),
        2074: (FEB, 26),
        2075: (FEB, 15),
        2076: (FEB, 5),
        2077: (JAN, 25),
    }

    ALI_BIRTHDAY_DATES = {
        1925: (FEB, 7),
        1926: (JAN, 27),
        1927: (JAN, 17),
        1928: ((JAN, 6), (DEC, 25)),
        1929: (DEC, 14),
        1930: (DEC, 3),
        1931: (NOV, 23),
        1932: (NOV, 12),
        1933: (NOV, 1),
        1934: (OCT, 22),
        1935: (OCT, 11),
        1936: (SEP, 29),
        1937: (SEP, 18),
        1938: (SEP, 7),
        1939: (AUG, 28),
        1940: (AUG, 17),
        1941: (AUG, 5),
        1942: (JUL, 26),
        1943: (JUL, 15),
        1944: (JUL, 3),
        1945: (JUN, 23),
        1946: (JUN, 12),
        1947: (JUN, 2),
        1948: (MAY, 21),
        1949: (MAY, 10),
        1950: (APR, 30),
        1951: (APR, 20),
        1952: (APR, 8),
        1953: (MAR, 29),
        1954: (MAR, 18),
        1955: (MAR, 7),
        1956: (FEB, 25),
        1957: (FEB, 13),
        1958: (FEB, 2),
        1959: (JAN, 23),
        1960: ((JAN, 12), (DEC, 31)),
        1961: (DEC, 21),
        1962: (DEC, 10),
        1963: (NOV, 29),
        1964: (NOV, 17),
        1965: (NOV, 6),
        1966: (OCT, 27),
        1967: (OCT, 16),
        1968: (OCT, 5),
        1969: (SEP, 24),
        1970: (SEP, 14),
        1971: (SEP, 3),
        1972: (AUG, 22),
        1973: (AUG, 11),
        1974: (AUG, 1),
        1975: (JUL, 22),
        1976: (JUL, 10),
        1977: (JUN, 29),
        1978: (JUN, 18),
        1979: (JUN, 8),
        1980: (MAY, 27),
        1981: (MAY, 17),
        1982: (MAY, 6),
        1983: (APR, 26),
        1984: (APR, 14),
        1985: (APR, 3),
        1986: (MAR, 23),
        1987: (MAR, 13),
        1988: (MAR, 1),
        1989: (FEB, 19),
        1990: (FEB, 8),
        1991: (JAN, 28),
        1992: (JAN, 17),
        1993: ((JAN, 6), (DEC, 26)),
        1994: (DEC, 15),
        1995: (DEC, 5),
        1996: (NOV, 24),
        1997: (NOV, 13),
        1998: (NOV, 2),
        1999: (OCT, 22),
        2000: (OCT, 10),
        2001: (SEP, 30),
        2002: (SEP, 20),
        2003: (SEP, 10),
        2004: (AUG, 29),
        2005: (AUG, 18),
        2006: (AUG, 7),
        2007: (JUL, 27),
        2008: (JUL, 16),
        2009: (JUL, 6),
        2010: (JUN, 25),
        2011: (JUN, 15),
        2012: (JUN, 3),
        2013: (MAY, 23),
        2014: (MAY, 12),
        2015: (MAY, 2),
        2016: (APR, 20),
        2017: (APR, 10),
        2018: (MAR, 30),
        2019: (MAR, 20),
        2020: (MAR, 8),
        2021: (FEB, 25),
        2022: (FEB, 14),
        2023: (FEB, 4),
        2024: (JAN, 25),
        2025: (JAN, 13),
        2026: ((JAN, 2), (DEC, 22)),
        2027: (DEC, 11),
        2028: (NOV, 30),
        2029: (NOV, 19),
        2030: (NOV, 9),
        2031: (OCT, 29),
        2032: (OCT, 18),
        2033: (OCT, 7),
        2034: (SEP, 26),
        2035: (SEP, 15),
        2036: (SEP, 4),
        2037: (AUG, 24),
        2038: (AUG, 14),
        2039: (AUG, 3),
        2040: (JUL, 22),
        2041: (JUL, 11),
        2042: (JUL, 1),
        2043: (JUN, 20),
        2044: (JUN, 9),
        2045: (MAY, 30),
        2046: (MAY, 19),
        2047: (MAY, 8),
        2048: (APR, 26),
        2049: (APR, 15),
        2050: (APR, 5),
        2051: (MAR, 26),
        2052: (MAR, 14),
        2053: (MAR, 4),
        2054: (FEB, 21),
        2055: (FEB, 10),
        2056: (JAN, 30),
        2057: (JAN, 18),
        2058: ((JAN, 8), (DEC, 29)),
        2059: (DEC, 18),
        2060: (DEC, 6),
        2061: (NOV, 25),
        2062: (NOV, 15),
        2063: (NOV, 4),
        2064: (OCT, 24),
        2065: (OCT, 13),
        2066: (OCT, 3),
        2067: (SEP, 22),
        2068: (SEP, 10),
        2069: (AUG, 30),
        2070: (AUG, 20),
        2071: (AUG, 9),
        2072: (JUL, 29),
        2073: (JUL, 18),
        2074: (JUL, 8),
        2075: (JUN, 27),
        2076: (JUN, 15),
        2077: (JUN, 4),
    }

    ALI_DEATH_DATES = {
        1925: (APR, 16),
        1926: (APR, 4),
        1927: (MAR, 24),
        1928: (MAR, 13),
        1929: (MAR, 2),
        1930: (FEB, 20),
        1931: (FEB, 9),
        1932: (JAN, 29),
        1933: (JAN, 17),
        1934: ((JAN, 7), (DEC, 28)),
        1935: (DEC, 17),
        1936: (DEC, 5),
        1937: (NOV, 25),
        1938: (NOV, 13),
        1939: (NOV, 3),
        1940: (OCT, 23),
        1941: (OCT, 11),
        1942: (OCT, 1),
        1943: (SEP, 20),
        1944: (SEP, 8),
        1945: (AUG, 28),
        1946: (AUG, 18),
        1947: (AUG, 8),
        1948: (JUL, 27),
        1949: (JUL, 16),
        1950: (JUL, 7),
        1951: (JUN, 26),
        1952: (JUN, 14),
        1953: (JUN, 3),
        1954: (MAY, 24),
        1955: (MAY, 14),
        1956: (MAY, 2),
        1957: (APR, 21),
        1958: (APR, 10),
        1959: (MAR, 31),
        1960: (MAR, 19),
        1961: (MAR, 8),
        1962: (FEB, 25),
        1963: (FEB, 15),
        1964: (FEB, 4),
        1965: (JAN, 23),
        1966: (JAN, 12),
        1967: ((JAN, 2), (DEC, 22)),
        1968: (DEC, 11),
        1969: (NOV, 30),
        1970: (NOV, 21),
        1971: (NOV, 9),
        1972: (OCT, 28),
        1973: (OCT, 17),
        1974: (OCT, 7),
        1975: (SEP, 26),
        1976: (SEP, 15),
        1977: (SEP, 4),
        1978: (AUG, 25),
        1979: (AUG, 14),
        1980: (AUG, 2),
        1981: (JUL, 22),
        1982: (JUL, 12),
        1983: (JUL, 2),
        1984: (JUN, 20),
        1985: (JUN, 9),
        1986: (MAY, 29),
        1987: (MAY, 19),
        1988: (MAY, 7),
        1989: (APR, 27),
        1990: (APR, 16),
        1991: (APR, 6),
        1992: (MAR, 25),
        1993: (MAR, 14),
        1994: (MAR, 3),
        1995: (FEB, 20),
        1996: (FEB, 10),
        1997: (JAN, 30),
        1998: (JAN, 19),
        1999: ((JAN, 8), (DEC, 29)),
        2000: (DEC, 17),
        2001: (DEC, 6),
        2002: (NOV, 26),
        2003: (NOV, 15),
        2004: (NOV, 4),
        2005: (OCT, 24),
        2006: (OCT, 14),
        2007: (OCT, 3),
        2008: (SEP, 21),
        2009: (SEP, 11),
        2010: (AUG, 31),
        2011: (AUG, 21),
        2012: (AUG, 9),
        2013: (JUL, 29),
        2014: (JUL, 18),
        2015: (JUL, 8),
        2016: (JUN, 26),
        2017: (JUN, 16),
        2018: (JUN, 5),
        2019: (MAY, 26),
        2020: (MAY, 14),
        2021: (MAY, 3),
        2022: (APR, 22),
        2023: (APR, 12),
        2024: (MAR, 31),
        2025: (MAR, 21),
        2026: (MAR, 10),
        2027: (FEB, 28),
        2028: (FEB, 17),
        2029: (FEB, 5),
        2030: (JAN, 25),
        2031: (JAN, 15),
        2032: ((JAN, 4), (DEC, 24)),
        2033: (DEC, 13),
        2034: (DEC, 2),
        2035: (NOV, 21),
        2036: (NOV, 9),
        2037: (OCT, 30),
        2038: (OCT, 20),
        2039: (OCT, 9),
        2040: (SEP, 27),
        2041: (SEP, 17),
        2042: (SEP, 6),
        2043: (AUG, 26),
        2044: (AUG, 15),
        2045: (AUG, 4),
        2046: (JUL, 25),
        2047: (JUL, 14),
        2048: (JUL, 2),
        2049: (JUN, 22),
        2050: (JUN, 11),
        2051: (MAY, 31),
        2052: (MAY, 20),
        2053: (MAY, 10),
        2054: (APR, 29),
        2055: (APR, 18),
        2056: (APR, 6),
        2057: (MAR, 26),
        2058: (MAR, 16),
        2059: (MAR, 6),
        2060: (FEB, 23),
        2061: (FEB, 12),
        2062: (FEB, 1),
        2063: (JAN, 21),
        2064: ((JAN, 10), (DEC, 29)),
        2065: (DEC, 19),
        2066: (DEC, 9),
        2067: (NOV, 28),
        2068: (NOV, 16),
        2069: (NOV, 5),
        2070: (OCT, 25),
        2071: (OCT, 15),
        2072: (OCT, 3),
        2073: (SEP, 23),
        2074: (SEP, 12),
        2075: (SEP, 2),
        2076: (AUG, 21),
        2077: (AUG, 10),
    }

    ARBAEEN_DATES = {
        1924: (SEP, 19),
        1925: (SEP, 9),
        1926: (AUG, 29),
        1927: (AUG, 18),
        1928: (AUG, 6),
        1929: (JUL, 27),
        1930: (JUL, 16),
        1931: (JUL, 6),
        1932: (JUN, 25),
        1933: (JUN, 14),
        1934: (JUN, 3),
        1935: (MAY, 23),
        1936: (MAY, 12),
        1937: (MAY, 1),
        1938: (APR, 20),
        1939: (APR, 10),
        1940: (MAR, 29),
        1941: (MAR, 18),
        1942: (MAR, 8),
        1943: (FEB, 25),
        1944: (FEB, 14),
        1945: (FEB, 3),
        1946: (JAN, 23),
        1947: (JAN, 13),
        1948: ((JAN, 2), (DEC, 21)),
        1949: (DEC, 11),
        1950: (DEC, 1),
        1951: (NOV, 20),
        1952: (NOV, 8),
        1953: (OCT, 28),
        1954: (OCT, 18),
        1955: (OCT, 8),
        1956: (SEP, 25),
        1957: (SEP, 15),
        1958: (SEP, 5),
        1959: (AUG, 24),
        1960: (AUG, 13),
        1961: (AUG, 2),
        1962: (JUL, 22),
        1963: (JUL, 11),
        1964: (JUN, 30),
        1965: (JUN, 19),
        1966: (JUN, 9),
        1967: (MAY, 29),
        1968: (MAY, 17),
        1969: (MAY, 7),
        1970: (APR, 27),
        1971: (APR, 16),
        1972: (APR, 4),
        1973: (MAR, 25),
        1974: (MAR, 14),
        1975: (MAR, 3),
        1976: (FEB, 20),
        1977: (FEB, 8),
        1978: (JAN, 29),
        1979: (JAN, 18),
        1980: ((JAN, 8), (DEC, 27)),
        1981: (DEC, 16),
        1982: (DEC, 5),
        1983: (NOV, 24),
        1984: (NOV, 13),
        1985: (NOV, 3),
        1986: (OCT, 23),
        1987: (OCT, 13),
        1988: (OCT, 1),
        1989: (SEP, 20),
        1990: (SEP, 9),
        1991: (AUG, 30),
        1992: (AUG, 18),
        1993: (AUG, 8),
        1994: (JUL, 28),
        1995: (JUL, 18),
        1996: (JUL, 6),
        1997: (JUN, 25),
        1998: (JUN, 14),
        1999: (JUN, 4),
        2000: (MAY, 24),
        2001: (MAY, 14),
        2002: (MAY, 3),
        2003: (APR, 22),
        2004: (APR, 10),
        2005: (MAR, 30),
        2006: (MAR, 20),
        2007: (MAR, 10),
        2008: (FEB, 27),
        2009: (FEB, 15),
        2010: (FEB, 4),
        2011: (JAN, 24),
        2012: (JAN, 14),
        2013: ((JAN, 2), (DEC, 23)),
        2014: (DEC, 12),
        2015: (DEC, 2),
        2016: (NOV, 20),
        2017: (NOV, 9),
        2018: (OCT, 29),
        2019: (OCT, 19),
        2020: (OCT, 7),
        2021: (SEP, 27),
        2022: (SEP, 16),
        2023: (SEP, 5),
        2024: (AUG, 24),
        2025: (AUG, 14),
        2026: (AUG, 3),
        2027: (JUL, 24),
        2028: (JUL, 13),
        2029: (JUL, 2),
        2030: (JUN, 21),
        2031: (JUN, 10),
        2032: (MAY, 29),
        2033: (MAY, 19),
        2034: (MAY, 9),
        2035: (APR, 28),
        2036: (APR, 17),
        2037: (APR, 6),
        2038: (MAR, 26),
        2039: (MAR, 15),
        2040: (MAR, 4),
        2041: (FEB, 21),
        2042: (FEB, 11),
        2043: (JAN, 31),
        2044: (JAN, 21),
        2045: ((JAN, 9), (DEC, 29)),
        2046: (DEC, 18),
        2047: (DEC, 8),
        2048: (NOV, 26),
        2049: (NOV, 16),
        2050: (NOV, 5),
        2051: (OCT, 25),
        2052: (OCT, 13),
        2053: (OCT, 2),
        2054: (SEP, 22),
        2055: (SEP, 12),
        2056: (AUG, 31),
        2057: (AUG, 20),
        2058: (AUG, 9),
        2059: (JUL, 30),
        2060: (JUL, 18),
        2061: (JUL, 8),
        2062: (JUN, 27),
        2063: (JUN, 17),
        2064: (JUN, 5),
        2065: (MAY, 25),
        2066: (MAY, 14),
        2067: (MAY, 4),
        2068: (APR, 22),
        2069: (APR, 12),
        2070: (APR, 2),
        2071: (MAR, 22),
        2072: (MAR, 10),
        2073: (FEB, 27),
        2074: (FEB, 16),
        2075: (FEB, 6),
        2076: (JAN, 26),
        2077: (JAN, 15),
    }

    ASHURA_DATES = {
        1924: (AUG, 10),
        1925: (AUG, 1),
        1926: (JUL, 20),
        1927: (JUL, 10),
        1928: (JUN, 28),
        1929: (JUN, 17),
        1930: (JUN, 6),
        1931: (MAY, 28),
        1932: (MAY, 16),
        1933: (MAY, 5),
        1934: (APR, 24),
        1935: (APR, 14),
        1936: (APR, 2),
        1937: (MAR, 23),
        1938: (MAR, 11),
        1939: (MAR, 1),
        1940: (FEB, 18),
        1941: (FEB, 6),
        1942: (JAN, 27),
        1943: (JAN, 16),
        1944: ((JAN, 5), (DEC, 25)),
        1945: (DEC, 14),
        1946: (DEC, 4),
        1947: (NOV, 23),
        1948: (NOV, 11),
        1949: (NOV, 1),
        1950: (OCT, 22),
        1951: (OCT, 11),
        1952: (SEP, 30),
        1953: (SEP, 19),
        1954: (SEP, 8),
        1955: (AUG, 29),
        1956: (AUG, 17),
        1957: (AUG, 6),
        1958: (JUL, 27),
        1959: (JUL, 16),
        1960: (JUL, 4),
        1961: (JUN, 23),
        1962: (JUN, 12),
        1963: (JUN, 2),
        1964: (MAY, 21),
        1965: (MAY, 10),
        1966: (APR, 30),
        1967: (APR, 20),
        1968: (APR, 8),
        1969: (MAR, 28),
        1970: (MAR, 18),
        1971: (MAR, 7),
        1972: (FEB, 25),
        1973: (FEB, 13),
        1974: (FEB, 2),
        1975: (JAN, 22),
        1976: ((JAN, 11), (DEC, 31)),
        1977: (DEC, 20),
        1978: (DEC, 10),
        1979: (NOV, 29),
        1980: (NOV, 18),
        1981: (NOV, 6),
        1982: (OCT, 27),
        1983: (OCT, 16),
        1984: (OCT, 5),
        1985: (SEP, 24),
        1986: (SEP, 14),
        1987: (SEP, 3),
        1988: (AUG, 22),
        1989: (AUG, 11),
        1990: (AUG, 1),
        1991: (JUL, 21),
        1992: (JUL, 10),
        1993: (JUN, 30),
        1994: (JUN, 19),
        1995: (JUN, 8),
        1996: (MAY, 27),
        1997: (MAY, 16),
        1998: (MAY, 6),
        1999: (APR, 26),
        2000: (APR, 15),
        2001: (APR, 4),
        2002: (MAR, 24),
        2003: (MAR, 13),
        2004: (MAR, 1),
        2005: (FEB, 19),
        2006: (FEB, 9),
        2007: (JAN, 29),
        2008: (JAN, 19),
        2009: ((JAN, 7), (DEC, 27)),
        2010: (DEC, 16),
        2011: (DEC, 5),
        2012: (NOV, 24),
        2013: (NOV, 13),
        2014: (NOV, 3),
        2015: (OCT, 23),
        2016: (OCT, 11),
        2017: (SEP, 30),
        2018: (SEP, 20),
        2019: (SEP, 9),
        2020: (AUG, 29),
        2021: (AUG, 18),
        2022: (AUG, 8),
        2023: (JUL, 28),
        2024: (JUL, 16),
        2025: (JUL, 5),
        2026: (JUN, 25),
        2027: (JUN, 15),
        2028: (JUN, 3),
        2029: (MAY, 23),
        2030: (MAY, 12),
        2031: (MAY, 2),
        2032: (APR, 20),
        2033: (APR, 10),
        2034: (MAR, 30),
        2035: (MAR, 20),
        2036: (MAR, 8),
        2037: (FEB, 25),
        2038: (FEB, 14),
        2039: (FEB, 4),
        2040: (JAN, 24),
        2041: (JAN, 13),
        2042: ((JAN, 2), (DEC, 23)),
        2043: (DEC, 12),
        2044: (NOV, 30),
        2045: (NOV, 19),
        2046: (NOV, 9),
        2047: (OCT, 29),
        2048: (OCT, 18),
        2049: (OCT, 7),
        2050: (SEP, 26),
        2051: (SEP, 15),
        2052: (SEP, 4),
        2053: (AUG, 24),
        2054: (AUG, 14),
        2055: (AUG, 3),
        2056: (JUL, 23),
        2057: (JUL, 12),
        2058: (JUL, 1),
        2059: (JUN, 20),
        2060: (JUN, 9),
        2061: (MAY, 29),
        2062: (MAY, 19),
        2063: (MAY, 9),
        2064: (APR, 27),
        2065: (APR, 16),
        2066: (APR, 5),
        2067: (MAR, 25),
        2068: (MAR, 14),
        2069: (MAR, 4),
        2070: (FEB, 21),
        2071: (FEB, 10),
        2072: (JAN, 30),
        2073: (JAN, 18),
        2074: ((JAN, 8), (DEC, 28)),
        2075: (DEC, 18),
        2076: (DEC, 6),
    }

    EID_AL_ADHA_DATES = {
        1925: (JUL, 2),
        1926: (JUN, 21),
        1927: (JUN, 10),
        1928: (MAY, 30),
        1929: (MAY, 19),
        1930: (MAY, 9),
        1931: (APR, 28),
        1932: (APR, 16),
        1933: (APR, 5),
        1934: (MAR, 26),
        1935: (MAR, 15),
        1936: (MAR, 4),
        1937: (FEB, 21),
        1938: (FEB, 10),
        1939: (JAN, 30),
        1940: (JAN, 20),
        1941: ((JAN, 8), (DEC, 28)),
        1942: (DEC, 18),
        1943: (DEC, 7),
        1944: (NOV, 25),
        1945: (NOV, 15),
        1946: (NOV, 4),
        1947: (OCT, 25),
        1948: (OCT, 13),
        1949: (OCT, 2),
        1950: (SEP, 23),
        1951: (SEP, 12),
        1952: (AUG, 31),
        1953: (AUG, 20),
        1954: (AUG, 9),
        1955: (JUL, 30),
        1956: (JUL, 19),
        1957: (JUL, 8),
        1958: (JUN, 27),
        1959: (JUN, 17),
        1960: (JUN, 4),
        1961: (MAY, 25),
        1962: (MAY, 14),
        1963: (MAY, 3),
        1964: (APR, 22),
        1965: (APR, 11),
        1966: (APR, 1),
        1967: (MAR, 21),
        1968: (MAR, 9),
        1969: (FEB, 27),
        1970: (FEB, 16),
        1971: (FEB, 6),
        1972: (JAN, 26),
        1973: (JAN, 14),
        1974: ((JAN, 3), (DEC, 24)),
        1975: (DEC, 13),
        1976: (DEC, 1),
        1977: (NOV, 21),
        1978: (NOV, 10),
        1979: (OCT, 31),
        1980: (OCT, 19),
        1981: (OCT, 8),
        1982: (SEP, 27),
        1983: (SEP, 17),
        1984: (SEP, 5),
        1985: (AUG, 26),
        1986: (AUG, 15),
        1987: (AUG, 4),
        1988: (JUL, 23),
        1989: (JUL, 13),
        1990: (JUL, 2),
        1991: (JUN, 22),
        1992: (JUN, 11),
        1993: (MAY, 31),
        1994: (MAY, 20),
        1995: (MAY, 9),
        1996: (APR, 27),
        1997: (APR, 17),
        1998: (APR, 7),
        1999: (MAR, 27),
        2000: (MAR, 16),
        2001: (MAR, 5),
        2002: (FEB, 22),
        2003: (FEB, 11),
        2004: (FEB, 1),
        2005: (JAN, 21),
        2006: ((JAN, 10), (DEC, 31)),
        2007: (DEC, 20),
        2008: (DEC, 8),
        2009: (NOV, 27),
        2010: (NOV, 16),
        2011: (NOV, 6),
        2012: (OCT, 26),
        2013: (OCT, 15),
        2014: (OCT, 4),
        2015: (SEP, 23),
        2016: (SEP, 11),
        2017: (SEP, 1),
        2018: (AUG, 21),
        2019: (AUG, 11),
        2020: (JUL, 31),
        2021: (JUL, 20),
        2022: (JUL, 9),
        2023: (JUN, 28),
        2024: (JUN, 16),
        2025: (JUN, 6),
        2026: (MAY, 27),
        2027: (MAY, 16),
        2028: (MAY, 5),
        2029: (APR, 24),
        2030: (APR, 13),
        2031: (APR, 2),
        2032: (MAR, 22),
        2033: (MAR, 11),
        2034: (MAR, 1),
        2035: (FEB, 18),
        2036: (FEB, 7),
        2037: (JAN, 26),
        2038: (JAN, 16),
        2039: ((JAN, 5), (DEC, 26)),
        2040: (DEC, 14),
        2041: (DEC, 4),
        2042: (NOV, 23),
        2043: (NOV, 12),
        2044: (OCT, 31),
        2045: (OCT, 21),
        2046: (OCT, 10),
        2047: (SEP, 30),
        2048: (SEP, 19),
        2049: (SEP, 8),
        2050: (AUG, 28),
        2051: (AUG, 17),
        2052: (AUG, 5),
        2053: (JUL, 26),
        2054: (JUL, 15),
        2055: (JUL, 5),
        2056: (JUN, 23),
        2057: (JUN, 12),
        2058: (JUN, 1),
        2059: (MAY, 22),
        2060: (MAY, 10),
        2061: (APR, 30),
        2062: (APR, 20),
        2063: (APR, 9),
        2064: (MAR, 28),
        2065: (MAR, 17),
        2066: (MAR, 6),
        2067: (FEB, 24),
        2068: (FEB, 13),
        2069: (FEB, 2),
        2070: (JAN, 22),
        2071: ((JAN, 11), (DEC, 31)),
        2072: (DEC, 20),
        2073: (DEC, 9),
        2074: (NOV, 29),
        2075: (NOV, 18),
        2076: (NOV, 7),
        2077: (OCT, 27),
    }

    EID_AL_FITR_DATES = {
        1925: (APR, 24),
        1926: (APR, 14),
        1927: (APR, 3),
        1928: (MAR, 22),
        1929: (MAR, 12),
        1930: (MAR, 1),
        1931: (FEB, 19),
        1932: (FEB, 8),
        1933: (JAN, 27),
        1934: (JAN, 17),
        1935: ((JAN, 7), (DEC, 27)),
        1936: (DEC, 15),
        1937: (DEC, 4),
        1938: (NOV, 23),
        1939: (NOV, 12),
        1940: (NOV, 1),
        1941: (OCT, 21),
        1942: (OCT, 11),
        1943: (SEP, 30),
        1944: (SEP, 18),
        1945: (SEP, 7),
        1946: (AUG, 28),
        1947: (AUG, 18),
        1948: (AUG, 6),
        1949: (JUL, 26),
        1950: (JUL, 16),
        1951: (JUL, 6),
        1952: (JUN, 23),
        1953: (JUN, 13),
        1954: (JUN, 2),
        1955: (MAY, 23),
        1956: (MAY, 11),
        1957: (MAY, 1),
        1958: (APR, 20),
        1959: (APR, 10),
        1960: (MAR, 28),
        1961: (MAR, 18),
        1962: (MAR, 7),
        1963: (FEB, 24),
        1964: (FEB, 14),
        1965: (FEB, 2),
        1966: (JAN, 22),
        1967: (JAN, 12),
        1968: ((JAN, 1), (DEC, 21)),
        1969: (DEC, 10),
        1970: (NOV, 30),
        1971: (NOV, 19),
        1972: (NOV, 7),
        1973: (OCT, 27),
        1974: (OCT, 16),
        1975: (OCT, 6),
        1976: (SEP, 24),
        1977: (SEP, 14),
        1978: (SEP, 3),
        1979: (AUG, 23),
        1980: (AUG, 12),
        1981: (AUG, 1),
        1982: (JUL, 21),
        1983: (JUL, 11),
        1984: (JUN, 30),
        1985: (JUN, 19),
        1986: (JUN, 8),
        1987: (MAY, 28),
        1988: (MAY, 16),
        1989: (MAY, 6),
        1990: (APR, 26),
        1991: (APR, 15),
        1992: (APR, 4),
        1993: (MAR, 24),
        1994: (MAR, 13),
        1995: (MAR, 2),
        1996: (FEB, 19),
        1997: (FEB, 8),
        1998: (JAN, 29),
        1999: (JAN, 18),
        2000: ((JAN, 8), (DEC, 27)),
        2001: (DEC, 16),
        2002: (DEC, 5),
        2003: (NOV, 25),
        2004: (NOV, 14),
        2005: (NOV, 3),
        2006: (OCT, 23),
        2007: (OCT, 13),
        2008: (OCT, 1),
        2009: (SEP, 20),
        2010: (SEP, 10),
        2011: (AUG, 30),
        2012: (AUG, 19),
        2013: (AUG, 8),
        2014: (JUL, 28),
        2015: (JUL, 17),
        2016: (JUL, 6),
        2017: (JUN, 25),
        2018: (JUN, 15),
        2019: (JUN, 4),
        2020: (MAY, 24),
        2021: (MAY, 13),
        2022: (MAY, 2),
        2023: (APR, 21),
        2024: (APR, 10),
        2025: (MAR, 30),
        2026: (MAR, 20),
        2027: (MAR, 9),
        2028: (FEB, 26),
        2029: (FEB, 14),
        2030: (FEB, 4),
        2031: (JAN, 24),
        2032: (JAN, 14),
        2033: ((JAN, 2), (DEC, 23)),
        2034: (DEC, 12),
        2035: (DEC, 1),
        2036: (NOV, 19),
        2037: (NOV, 8),
        2038: (OCT, 29),
        2039: (OCT, 19),
        2040: (OCT, 7),
        2041: (SEP, 26),
        2042: (SEP, 15),
        2043: (SEP, 4),
        2044: (AUG, 24),
        2045: (AUG, 14),
        2046: (AUG, 3),
        2047: (JUL, 24),
        2048: (JUL, 12),
        2049: (JUL, 1),
        2050: (JUN, 20),
        2051: (JUN, 10),
        2052: (MAY, 29),
        2053: (MAY, 19),
        2054: (MAY, 9),
        2055: (APR, 28),
        2056: (APR, 16),
        2057: (APR, 5),
        2058: (MAR, 25),
        2059: (MAR, 15),
        2060: (MAR, 4),
        2061: (FEB, 21),
        2062: (FEB, 10),
        2063: (JAN, 30),
        2064: (JAN, 20),
        2065: ((JAN, 8), (DEC, 28)),
        2066: (DEC, 18),
        2067: (DEC, 8),
        2068: (NOV, 26),
        2069: (NOV, 15),
        2070: (NOV, 4),
        2071: (OCT, 24),
        2072: (OCT, 13),
        2073: (OCT, 2),
        2074: (SEP, 22),
        2075: (SEP, 11),
        2076: (AUG, 30),
        2077: (AUG, 19),
    }

    EID_AL_GHADIR_DATES = {
        1925: (JUL, 10),
        1926: (JUN, 29),
        1927: (JUN, 18),
        1928: (JUN, 7),
        1929: (MAY, 27),
        1930: (MAY, 17),
        1931: (MAY, 6),
        1932: (APR, 24),
        1933: (APR, 13),
        1934: (APR, 3),
        1935: (MAR, 23),
        1936: (MAR, 12),
        1937: (MAR, 1),
        1938: (FEB, 18),
        1939: (FEB, 7),
        1940: (JAN, 28),
        1941: (JAN, 16),
        1942: ((JAN, 5), (DEC, 26)),
        1943: (DEC, 15),
        1944: (DEC, 3),
        1945: (NOV, 23),
        1946: (NOV, 12),
        1947: (NOV, 2),
        1948: (OCT, 21),
        1949: (OCT, 10),
        1950: (OCT, 1),
        1951: (SEP, 20),
        1952: (SEP, 8),
        1953: (AUG, 28),
        1954: (AUG, 17),
        1955: (AUG, 7),
        1956: (JUL, 27),
        1957: (JUL, 16),
        1958: (JUL, 5),
        1959: (JUN, 25),
        1960: (JUN, 12),
        1961: (JUN, 2),
        1962: (MAY, 22),
        1963: (MAY, 11),
        1964: (APR, 30),
        1965: (APR, 19),
        1966: (APR, 9),
        1967: (MAR, 29),
        1968: (MAR, 17),
        1969: (MAR, 7),
        1970: (FEB, 24),
        1971: (FEB, 14),
        1972: (FEB, 3),
        1973: (JAN, 22),
        1974: (JAN, 11),
        1975: ((JAN, 1), (DEC, 21)),
        1976: (DEC, 9),
        1977: (NOV, 29),
        1978: (NOV, 18),
        1979: (NOV, 8),
        1980: (OCT, 27),
        1981: (OCT, 16),
        1982: (OCT, 5),
        1983: (SEP, 25),
        1984: (SEP, 13),
        1985: (SEP, 3),
        1986: (AUG, 23),
        1987: (AUG, 12),
        1988: (JUL, 31),
        1989: (JUL, 21),
        1990: (JUL, 10),
        1991: (JUN, 30),
        1992: (JUN, 19),
        1993: (JUN, 8),
        1994: (MAY, 28),
        1995: (MAY, 17),
        1996: (MAY, 5),
        1997: (APR, 25),
        1998: (APR, 15),
        1999: (APR, 4),
        2000: (MAR, 24),
        2001: (MAR, 13),
        2002: (MAR, 2),
        2003: (FEB, 19),
        2004: (FEB, 9),
        2005: (JAN, 29),
        2006: (JAN, 18),
        2007: ((JAN, 8), (DEC, 28)),
        2008: (DEC, 16),
        2009: (DEC, 5),
        2010: (NOV, 24),
        2011: (NOV, 14),
        2012: (NOV, 3),
        2013: (OCT, 23),
        2014: (OCT, 12),
        2015: (OCT, 1),
        2016: (SEP, 19),
        2017: (SEP, 9),
        2018: (AUG, 29),
        2019: (AUG, 19),
        2020: (AUG, 8),
        2021: (JUL, 28),
        2022: (JUL, 17),
        2023: (JUL, 6),
        2024: (JUN, 24),
        2025: (JUN, 14),
        2026: (JUN, 4),
        2027: (MAY, 24),
        2028: (MAY, 13),
        2029: (MAY, 2),
        2030: (APR, 21),
        2031: (APR, 10),
        2032: (MAR, 30),
        2033: (MAR, 19),
        2034: (MAR, 9),
        2035: (FEB, 26),
        2036: (FEB, 15),
        2037: (FEB, 3),
        2038: (JAN, 24),
        2039: (JAN, 13),
        2040: ((JAN, 3), (DEC, 22)),
        2041: (DEC, 12),
        2042: (DEC, 1),
        2043: (NOV, 20),
        2044: (NOV, 8),
        2045: (OCT, 29),
        2046: (OCT, 18),
        2047: (OCT, 8),
        2048: (SEP, 27),
        2049: (SEP, 16),
        2050: (SEP, 5),
        2051: (AUG, 25),
        2052: (AUG, 13),
        2053: (AUG, 3),
        2054: (JUL, 23),
        2055: (JUL, 13),
        2056: (JUL, 1),
        2057: (JUN, 20),
        2058: (JUN, 9),
        2059: (MAY, 30),
        2060: (MAY, 18),
        2061: (MAY, 8),
        2062: (APR, 28),
        2063: (APR, 17),
        2064: (APR, 5),
        2065: (MAR, 25),
        2066: (MAR, 14),
        2067: (MAR, 4),
        2068: (FEB, 21),
        2069: (FEB, 10),
        2070: (JAN, 30),
        2071: (JAN, 19),
        2072: ((JAN, 8), (DEC, 28)),
        2073: (DEC, 17),
        2074: (DEC, 7),
        2075: (NOV, 26),
        2076: (NOV, 15),
        2077: (NOV, 4),
    }

    FATIMA_DEATH_DATES = {
        1924: (DEC, 29),
        1925: (DEC, 19),
        1926: (DEC, 9),
        1927: (NOV, 27),
        1928: (NOV, 15),
        1929: (NOV, 5),
        1930: (OCT, 25),
        1931: (OCT, 15),
        1932: (OCT, 4),
        1933: (SEP, 23),
        1934: (SEP, 12),
        1935: (SEP, 1),
        1936: (AUG, 21),
        1937: (AUG, 10),
        1938: (JUL, 30),
        1939: (JUL, 20),
        1940: (JUL, 9),
        1941: (JUN, 27),
        1942: (JUN, 17),
        1943: (JUN, 6),
        1944: (MAY, 25),
        1945: (MAY, 15),
        1946: (MAY, 4),
        1947: (APR, 24),
        1948: (APR, 12),
        1949: (APR, 1),
        1950: (MAR, 22),
        1951: (MAR, 12),
        1952: (FEB, 28),
        1953: (FEB, 17),
        1954: (FEB, 6),
        1955: (JAN, 27),
        1956: (JAN, 17),
        1957: ((JAN, 4), (DEC, 24)),
        1958: (DEC, 15),
        1959: (DEC, 3),
        1960: (NOV, 22),
        1961: (NOV, 11),
        1962: (OCT, 31),
        1963: (OCT, 21),
        1964: (OCT, 9),
        1965: (SEP, 28),
        1966: (SEP, 18),
        1967: (SEP, 7),
        1968: (AUG, 27),
        1969: (AUG, 16),
        1970: (AUG, 6),
        1971: (JUL, 26),
        1972: (JUL, 14),
        1973: (JUL, 3),
        1974: (JUN, 23),
        1975: (JUN, 12),
        1976: (JUN, 1),
        1977: (MAY, 21),
        1978: (MAY, 10),
        1979: (APR, 29),
        1980: (APR, 18),
        1981: (APR, 7),
        1982: (MAR, 28),
        1983: (MAR, 17),
        1984: (MAR, 5),
        1985: (FEB, 22),
        1986: (FEB, 12),
        1987: (FEB, 1),
        1988: (JAN, 22),
        1989: ((JAN, 10), (DEC, 31)),
        1990: (DEC, 20),
        1991: (DEC, 9),
        1992: (NOV, 27),
        1993: (NOV, 16),
        1994: (NOV, 6),
        1995: (OCT, 27),
        1996: (OCT, 15),
        1997: (OCT, 4),
        1998: (SEP, 23),
        1999: (SEP, 13),
        2000: (SEP, 1),
        2001: (AUG, 22),
        2002: (AUG, 12),
        2003: (AUG, 1),
        2004: (JUL, 20),
        2005: (JUL, 9),
        2006: (JUN, 29),
        2007: (JUN, 18),
        2008: (JUN, 7),
        2009: (MAY, 27),
        2010: (MAY, 17),
        2011: (MAY, 6),
        2012: (APR, 24),
        2013: (APR, 13),
        2014: (APR, 3),
        2015: (MAR, 23),
        2016: (MAR, 12),
        2017: (MAR, 2),
        2018: (FEB, 19),
        2019: (FEB, 8),
        2020: (JAN, 28),
        2021: (JAN, 16),
        2022: ((JAN, 6), (DEC, 27)),
        2023: (DEC, 16),
        2024: (DEC, 4),
        2025: (NOV, 24),
        2026: (NOV, 13),
        2027: (NOV, 2),
        2028: (OCT, 21),
        2029: (OCT, 11),
        2030: (OCT, 1),
        2031: (SEP, 20),
        2032: (SEP, 8),
        2033: (AUG, 28),
        2034: (AUG, 17),
        2035: (AUG, 7),
        2036: (JUL, 26),
        2037: (JUL, 16),
        2038: (JUL, 5),
        2039: (JUN, 25),
        2040: (JUN, 13),
        2041: (JUN, 2),
        2042: (MAY, 22),
        2043: (MAY, 12),
        2044: (MAY, 1),
        2045: (APR, 20),
        2046: (APR, 9),
        2047: (MAR, 29),
        2048: (MAR, 18),
        2049: (MAR, 7),
        2050: (FEB, 25),
        2051: (FEB, 14),
        2052: (FEB, 4),
        2053: (JAN, 23),
        2054: (JAN, 12),
        2055: ((JAN, 1), (DEC, 21)),
        2056: (DEC, 10),
        2057: (NOV, 29),
        2058: (NOV, 19),
        2059: (NOV, 8),
        2060: (OCT, 27),
        2061: (OCT, 17),
        2062: (OCT, 6),
        2063: (SEP, 26),
        2064: (SEP, 14),
        2065: (SEP, 4),
        2066: (AUG, 24),
        2067: (AUG, 13),
        2068: (AUG, 1),
        2069: (JUL, 22),
        2070: (JUL, 11),
        2071: (JUL, 1),
        2072: (JUN, 19),
        2073: (JUN, 9),
        2074: (MAY, 29),
        2075: (MAY, 18),
        2076: (MAY, 6),
        2077: (APR, 26),
    }

    GRAND_MAGAL_OF_TOUBA_DATES = {
        1924: (SEP, 17),
        1925: (SEP, 7),
        1926: (AUG, 27),
        1927: (AUG, 16),
        1928: (AUG, 4),
        1929: (JUL, 25),
        1930: (JUL, 14),
        1931: (JUL, 4),
        1932: (JUN, 23),
        1933: (JUN, 12),
        1934: (JUN, 1),
        1935: (MAY, 21),
        1936: (MAY, 10),
        1937: (APR, 29),
        1938: (APR, 18),
        1939: (APR, 8),
        1940: (MAR, 27),
        1941: (MAR, 16),
        1942: (MAR, 6),
        1943: (FEB, 23),
        1944: (FEB, 12),
        1945: (FEB, 1),
        1946: (JAN, 21),
        1947: ((JAN, 11), (DEC, 31)),
        1948: (DEC, 19),
        1949: (DEC, 9),
        1950: (NOV, 29),
        1951: (NOV, 18),
        1952: (NOV, 6),
        1953: (OCT, 26),
        1954: (OCT, 16),
        1955: (OCT, 6),
        1956: (SEP, 23),
        1957: (SEP, 13),
        1958: (SEP, 3),
        1959: (AUG, 22),
        1960: (AUG, 11),
        1961: (JUL, 31),
        1962: (JUL, 20),
        1963: (JUL, 9),
        1964: (JUN, 28),
        1965: (JUN, 17),
        1966: (JUN, 7),
        1967: (MAY, 27),
        1968: (MAY, 15),
        1969: (MAY, 5),
        1970: (APR, 25),
        1971: (APR, 14),
        1972: (APR, 2),
        1973: (MAR, 23),
        1974: (MAR, 12),
        1975: (MAR, 1),
        1976: (FEB, 18),
        1977: (FEB, 6),
        1978: (JAN, 27),
        1979: (JAN, 16),
        1980: ((JAN, 6), (DEC, 25)),
        1981: (DEC, 14),
        1982: (DEC, 3),
        1983: (NOV, 22),
        1984: (NOV, 11),
        1985: (NOV, 1),
        1986: (OCT, 21),
        1987: (OCT, 11),
        1988: (SEP, 29),
        1989: (SEP, 18),
        1990: (SEP, 7),
        1991: (AUG, 28),
        1992: (AUG, 16),
        1993: (AUG, 6),
        1994: (JUL, 26),
        1995: (JUL, 16),
        1996: (JUL, 4),
        1997: (JUN, 23),
        1998: (JUN, 12),
        1999: (JUN, 2),
        2000: (MAY, 22),
        2001: (MAY, 12),
        2002: (MAY, 1),
        2003: (APR, 20),
        2004: (APR, 8),
        2005: (MAR, 28),
        2006: (MAR, 18),
        2007: (MAR, 8),
        2008: (FEB, 25),
        2009: (FEB, 13),
        2010: (FEB, 2),
        2011: (JAN, 22),
        2012: ((JAN, 12), (DEC, 31)),
        2013: (DEC, 21),
        2014: (DEC, 10),
        2015: (NOV, 30),
        2016: (NOV, 18),
        2017: (NOV, 7),
        2018: (OCT, 27),
        2019: (OCT, 17),
        2020: (OCT, 5),
        2021: (SEP, 25),
        2022: (SEP, 14),
        2023: (SEP, 3),
        2024: (AUG, 22),
        2025: (AUG, 12),
        2026: (AUG, 1),
        2027: (JUL, 22),
        2028: (JUL, 11),
        2029: (JUN, 30),
        2030: (JUN, 19),
        2031: (JUN, 8),
        2032: (MAY, 27),
        2033: (MAY, 17),
        2034: (MAY, 7),
        2035: (APR, 26),
        2036: (APR, 15),
        2037: (APR, 4),
        2038: (MAR, 24),
        2039: (MAR, 13),
        2040: (MAR, 2),
        2041: (FEB, 19),
        2042: (FEB, 9),
        2043: (JAN, 29),
        2044: (JAN, 19),
        2045: ((JAN, 7), (DEC, 27)),
        2046: (DEC, 16),
        2047: (DEC, 6),
        2048: (NOV, 24),
        2049: (NOV, 14),
        2050: (NOV, 3),
        2051: (OCT, 23),
        2052: (OCT, 11),
        2053: (SEP, 30),
        2054: (SEP, 20),
        2055: (SEP, 10),
        2056: (AUG, 29),
        2057: (AUG, 18),
        2058: (AUG, 7),
        2059: (JUL, 28),
        2060: (JUL, 16),
        2061: (JUL, 6),
        2062: (JUN, 25),
        2063: (JUN, 15),
        2064: (JUN, 3),
        2065: (MAY, 23),
        2066: (MAY, 12),
        2067: (MAY, 2),
        2068: (APR, 20),
        2069: (APR, 10),
        2070: (MAR, 31),
        2071: (MAR, 20),
        2072: (MAR, 8),
        2073: (FEB, 25),
        2074: (FEB, 14),
        2075: (FEB, 4),
        2076: (JAN, 24),
        2077: (JAN, 13),
    }

    HARI_HOL_JOHOR_DATES = {
        1924: (SEP, 5),
        1925: (AUG, 26),
        1926: (AUG, 15),
        1927: (AUG, 4),
        1928: (JUL, 23),
        1929: (JUL, 13),
        1930: (JUL, 2),
        1931: (JUN, 22),
        1932: (JUN, 11),
        1933: (MAY, 31),
        1934: (MAY, 20),
        1935: (MAY, 9),
        1936: (APR, 28),
        1937: (APR, 17),
        1938: (APR, 6),
        1939: (MAR, 27),
        1940: (MAR, 15),
        1941: (MAR, 4),
        1942: (FEB, 22),
        1943: (FEB, 11),
        1944: (JAN, 31),
        1945: (JAN, 20),
        1946: ((JAN, 9), (DEC, 30)),
        1947: (DEC, 19),
        1948: (DEC, 7),
        1949: (NOV, 27),
        1950: (NOV, 17),
        1951: (NOV, 6),
        1952: (OCT, 25),
        1953: (OCT, 14),
        1954: (OCT, 4),
        1955: (SEP, 24),
        1956: (SEP, 11),
        1957: (SEP, 1),
        1958: (AUG, 22),
        1959: (AUG, 10),
        1960: (JUL, 30),
        1961: (JUL, 19),
        1962: (JUL, 8),
        1963: (JUN, 27),
        1964: (JUN, 16),
        1965: (JUN, 5),
        1966: (MAY, 26),
        1967: (MAY, 15),
        1968: (MAY, 3),
        1969: (APR, 23),
        1970: (APR, 13),
        1971: (APR, 2),
        1972: (MAR, 21),
        1973: (MAR, 11),
        1974: (FEB, 28),
        1975: (FEB, 17),
        1976: (FEB, 6),
        1977: (JAN, 25),
        1978: (JAN, 15),
        1979: ((JAN, 4), (DEC, 25)),
        1980: (DEC, 13),
        1981: (DEC, 2),
        1982: (NOV, 21),
        1983: (NOV, 10),
        1984: (OCT, 30),
        1985: (OCT, 20),
        1986: (OCT, 9),
        1987: (SEP, 29),
        1988: (SEP, 17),
        1989: (SEP, 6),
        1990: (AUG, 26),
        1991: (AUG, 16),
        1992: (AUG, 4),
        1993: (JUL, 25),
        1994: (JUL, 14),
        1995: (JUL, 4),
        1996: (JUN, 22),
        1997: (JUN, 11),
        1998: (MAY, 31),
        1999: (MAY, 21),
        2000: (MAY, 10),
        2001: (APR, 30),
        2002: (APR, 19),
        2003: (APR, 8),
        2004: (MAR, 27),
        2005: (MAR, 16),
        2006: (MAR, 6),
        2007: (FEB, 24),
        2008: (FEB, 13),
        2009: (FEB, 1),
        2010: (JAN, 21),
        2011: ((JAN, 10), (DEC, 31)),
        2012: (DEC, 19),
        2013: (DEC, 9),
        2014: (NOV, 28),
        2015: (NOV, 18),
        2016: (NOV, 6),
        2017: (OCT, 26),
        2018: (OCT, 15),
        2019: (OCT, 5),
        2020: (SEP, 23),
        2021: (SEP, 13),
        2022: (SEP, 2),
        2023: (AUG, 22),
        2024: (AUG, 10),
        2025: (JUL, 31),
        2026: (JUL, 20),
        2027: (JUL, 10),
        2028: (JUN, 29),
        2029: (JUN, 18),
        2030: (JUN, 7),
        2031: (MAY, 27),
        2032: (MAY, 15),
        2033: (MAY, 5),
        2034: (APR, 25),
        2035: (APR, 14),
        2036: (APR, 3),
        2037: (MAR, 23),
        2038: (MAR, 12),
        2039: (MAR, 1),
        2040: (FEB, 19),
        2041: (FEB, 7),
        2042: (JAN, 28),
        2043: (JAN, 17),
        2044: ((JAN, 7), (DEC, 26)),
        2045: (DEC, 15),
        2046: (DEC, 4),
        2047: (NOV, 24),
        2048: (NOV, 12),
        2049: (NOV, 2),
        2050: (OCT, 22),
        2051: (OCT, 11),
        2052: (SEP, 29),
        2053: (SEP, 18),
        2054: (SEP, 8),
        2055: (AUG, 29),
        2056: (AUG, 17),
        2057: (AUG, 6),
        2058: (JUL, 26),
        2059: (JUL, 16),
        2060: (JUL, 4),
        2061: (JUN, 24),
        2062: (JUN, 13),
        2063: (JUN, 3),
        2064: (MAY, 22),
        2065: (MAY, 11),
        2066: (APR, 30),
        2067: (APR, 20),
        2068: (APR, 8),
        2069: (MAR, 29),
        2070: (MAR, 19),
        2071: (MAR, 8),
        2072: (FEB, 25),
        2073: (FEB, 13),
        2074: (FEB, 2),
        2075: (JAN, 23),
        2076: (JAN, 12),
        2077: (JAN, 1),
    }

    HASAN_AL_ASKARI_DEATH_DATES = {
        1924: (OCT, 6),
        1925: (SEP, 26),
        1926: (SEP, 15),
        1927: (SEP, 4),
        1928: (AUG, 23),
        1929: (AUG, 13),
        1930: (AUG, 2),
        1931: (JUL, 24),
        1932: (JUL, 12),
        1933: (JUL, 1),
        1934: (JUN, 20),
        1935: (JUN, 10),
        1936: (MAY, 29),
        1937: (MAY, 18),
        1938: (MAY, 7),
        1939: (APR, 28),
        1940: (APR, 16),
        1941: (APR, 4),
        1942: (MAR, 25),
        1943: (MAR, 14),
        1944: (MAR, 2),
        1945: (FEB, 20),
        1946: (FEB, 9),
        1947: (JAN, 30),
        1948: (JAN, 19),
        1949: ((JAN, 7), (DEC, 28)),
        1950: (DEC, 18),
        1951: (DEC, 7),
        1952: (NOV, 26),
        1953: (NOV, 15),
        1954: (NOV, 4),
        1955: (OCT, 25),
        1956: (OCT, 13),
        1957: (OCT, 2),
        1958: (SEP, 22),
        1959: (SEP, 11),
        1960: (AUG, 30),
        1961: (AUG, 19),
        1962: (AUG, 8),
        1963: (JUL, 29),
        1964: (JUL, 17),
        1965: (JUL, 6),
        1966: (JUN, 27),
        1967: (JUN, 15),
        1968: (JUN, 4),
        1969: (MAY, 24),
        1970: (MAY, 14),
        1971: (MAY, 3),
        1972: (APR, 21),
        1973: (APR, 11),
        1974: (MAR, 31),
        1975: (MAR, 20),
        1976: (MAR, 8),
        1977: (FEB, 26),
        1978: (FEB, 15),
        1979: (FEB, 5),
        1980: (JAN, 26),
        1981: (JAN, 14),
        1982: ((JAN, 3), (DEC, 23)),
        1983: (DEC, 12),
        1984: (NOV, 30),
        1985: (NOV, 20),
        1986: (NOV, 10),
        1987: (OCT, 30),
        1988: (OCT, 18),
        1989: (OCT, 7),
        1990: (SEP, 27),
        1991: (SEP, 16),
        1992: (SEP, 5),
        1993: (AUG, 25),
        1994: (AUG, 15),
        1995: (AUG, 4),
        1996: (JUL, 23),
        1997: (JUL, 12),
        1998: (JUL, 2),
        1999: (JUN, 22),
        2000: (JUN, 10),
        2001: (MAY, 31),
        2002: (MAY, 20),
        2003: (MAY, 9),
        2004: (APR, 27),
        2005: (APR, 17),
        2006: (APR, 6),
        2007: (MAR, 27),
        2008: (MAR, 16),
        2009: (MAR, 5),
        2010: (FEB, 22),
        2011: (FEB, 11),
        2012: (JAN, 31),
        2013: (JAN, 20),
        2014: ((JAN, 9), (DEC, 30)),
        2015: (DEC, 19),
        2016: (DEC, 7),
        2017: (NOV, 26),
        2018: (NOV, 16),
        2019: (NOV, 5),
        2020: (OCT, 25),
        2021: (OCT, 14),
        2022: (OCT, 4),
        2023: (SEP, 23),
        2024: (SEP, 11),
        2025: (AUG, 31),
        2026: (AUG, 21),
        2027: (AUG, 10),
        2028: (JUL, 30),
        2029: (JUL, 20),
        2030: (JUL, 9),
        2031: (JUN, 28),
        2032: (JUN, 16),
        2033: (JUN, 5),
        2034: (MAY, 26),
        2035: (MAY, 16),
        2036: (MAY, 4),
        2037: (APR, 24),
        2038: (APR, 13),
        2039: (APR, 2),
        2040: (MAR, 21),
        2041: (MAR, 11),
        2042: (FEB, 28),
        2043: (FEB, 18),
        2044: (FEB, 7),
        2045: (JAN, 26),
        2046: (JAN, 15),
        2047: ((JAN, 4), (DEC, 25)),
        2048: (DEC, 14),
        2049: (DEC, 3),
        2050: (NOV, 22),
        2051: (NOV, 12),
        2052: (OCT, 31),
        2053: (OCT, 20),
        2054: (OCT, 9),
        2055: (SEP, 29),
        2056: (SEP, 18),
        2057: (SEP, 7),
        2058: (AUG, 27),
        2059: (AUG, 16),
        2060: (AUG, 4),
        2061: (JUL, 25),
        2062: (JUL, 15),
        2063: (JUL, 4),
        2064: (JUN, 23),
        2065: (JUN, 12),
        2066: (JUN, 1),
        2067: (MAY, 21),
        2068: (MAY, 10),
        2069: (APR, 29),
        2070: (APR, 19),
        2071: (APR, 9),
        2072: (MAR, 28),
        2073: (MAR, 17),
        2074: (MAR, 6),
        2075: (FEB, 23),
        2076: (FEB, 13),
        2077: (FEB, 2),
    }

    HIJRI_NEW_YEAR_DATES = {
        1924: (AUG, 1),
        1925: (JUL, 23),
        1926: (JUL, 11),
        1927: (JUL, 1),
        1928: (JUN, 19),
        1929: (JUN, 8),
        1930: (MAY, 28),
        1931: (MAY, 19),
        1932: (MAY, 7),
        1933: (APR, 26),
        1934: (APR, 15),
        1935: (APR, 5),
        1936: (MAR, 24),
        1937: (MAR, 14),
        1938: (MAR, 2),
        1939: (FEB, 20),
        1940: (FEB, 9),
        1941: (JAN, 28),
        1942: (JAN, 18),
        1943: ((JAN, 7), (DEC, 27)),
        1944: (DEC, 16),
        1945: (DEC, 5),
        1946: (NOV, 25),
        1947: (NOV, 14),
        1948: (NOV, 2),
        1949: (OCT, 23),
        1950: (OCT, 13),
        1951: (OCT, 2),
        1952: (SEP, 21),
        1953: (SEP, 10),
        1954: (AUG, 30),
        1955: (AUG, 20),
        1956: (AUG, 8),
        1957: (JUL, 28),
        1958: (JUL, 18),
        1959: (JUL, 7),
        1960: (JUN, 25),
        1961: (JUN, 14),
        1962: (JUN, 3),
        1963: (MAY, 24),
        1964: (MAY, 12),
        1965: (MAY, 1),
        1966: (APR, 21),
        1967: (APR, 11),
        1968: (MAR, 30),
        1969: (MAR, 19),
        1970: (MAR, 9),
        1971: (FEB, 26),
        1972: (FEB, 16),
        1973: (FEB, 4),
        1974: (JAN, 24),
        1975: (JAN, 13),
        1976: ((JAN, 2), (DEC, 22)),
        1977: (DEC, 11),
        1978: (DEC, 1),
        1979: (NOV, 20),
        1980: (NOV, 9),
        1981: (OCT, 28),
        1982: (OCT, 18),
        1983: (OCT, 7),
        1984: (SEP, 26),
        1985: (SEP, 15),
        1986: (SEP, 5),
        1987: (AUG, 25),
        1988: (AUG, 13),
        1989: (AUG, 2),
        1990: (JUL, 23),
        1991: (JUL, 12),
        1992: (JUL, 1),
        1993: (JUN, 21),
        1994: (JUN, 10),
        1995: (MAY, 30),
        1996: (MAY, 18),
        1997: (MAY, 7),
        1998: (APR, 27),
        1999: (APR, 17),
        2000: (APR, 6),
        2001: (MAR, 26),
        2002: (MAR, 15),
        2003: (MAR, 4),
        2004: (FEB, 21),
        2005: (FEB, 10),
        2006: (JAN, 31),
        2007: (JAN, 20),
        2008: ((JAN, 10), (DEC, 29)),
        2009: (DEC, 18),
        2010: (DEC, 7),
        2011: (NOV, 26),
        2012: (NOV, 15),
        2013: (NOV, 4),
        2014: (OCT, 25),
        2015: (OCT, 14),
        2016: (OCT, 2),
        2017: (SEP, 21),
        2018: (SEP, 11),
        2019: (AUG, 31),
        2020: (AUG, 20),
        2021: (AUG, 9),
        2022: (JUL, 30),
        2023: (JUL, 19),
        2024: (JUL, 7),
        2025: (JUN, 26),
        2026: (JUN, 16),
        2027: (JUN, 6),
        2028: (MAY, 25),
        2029: (MAY, 14),
        2030: (MAY, 3),
        2031: (APR, 23),
        2032: (APR, 11),
        2033: (APR, 1),
        2034: (MAR, 21),
        2035: (MAR, 11),
        2036: (FEB, 28),
        2037: (FEB, 16),
        2038: (FEB, 5),
        2039: (JAN, 26),
        2040: (JAN, 15),
        2041: ((JAN, 4), (DEC, 24)),
        2042: (DEC, 14),
        2043: (DEC, 3),
        2044: (NOV, 21),
        2045: (NOV, 10),
        2046: (OCT, 31),
        2047: (OCT, 20),
        2048: (OCT, 9),
        2049: (SEP, 28),
        2050: (SEP, 17),
        2051: (SEP, 6),
        2052: (AUG, 26),
        2053: (AUG, 15),
        2054: (AUG, 5),
        2055: (JUL, 25),
        2056: (JUL, 14),
        2057: (JUL, 3),
        2058: (JUN, 22),
        2059: (JUN, 11),
        2060: (MAY, 31),
        2061: (MAY, 20),
        2062: (MAY, 10),
        2063: (APR, 30),
        2064: (APR, 18),
        2065: (APR, 7),
        2066: (MAR, 27),
        2067: (MAR, 16),
        2068: (MAR, 5),
        2069: (FEB, 23),
        2070: (FEB, 12),
        2071: (FEB, 1),
        2072: (JAN, 21),
        2073: ((JAN, 9), (DEC, 30)),
        2074: (DEC, 19),
        2075: (DEC, 9),
        2076: (NOV, 27),
    }

    IMAM_MAHDI_BIRTHDAY_DATES = {
        1925: (MAR, 11),
        1926: (FEB, 28),
        1927: (FEB, 18),
        1928: (FEB, 7),
        1929: (JAN, 26),
        1930: (JAN, 15),
        1931: ((JAN, 4), (DEC, 25)),
        1932: (DEC, 13),
        1933: (DEC, 3),
        1934: (NOV, 22),
        1935: (NOV, 12),
        1936: (OCT, 31),
        1937: (OCT, 20),
        1938: (OCT, 9),
        1939: (SEP, 29),
        1940: (SEP, 17),
        1941: (SEP, 6),
        1942: (AUG, 27),
        1943: (AUG, 16),
        1944: (AUG, 4),
        1945: (JUL, 25),
        1946: (JUL, 14),
        1947: (JUL, 4),
        1948: (JUN, 22),
        1949: (JUN, 11),
        1950: (JUN, 1),
        1951: (MAY, 22),
        1952: (MAY, 9),
        1953: (APR, 29),
        1954: (APR, 18),
        1955: (APR, 8),
        1956: (MAR, 28),
        1957: (MAR, 17),
        1958: (MAR, 5),
        1959: (FEB, 24),
        1960: (FEB, 12),
        1961: (FEB, 1),
        1962: (JAN, 21),
        1963: ((JAN, 11), (DEC, 31)),
        1964: (DEC, 19),
        1965: (DEC, 8),
        1966: (NOV, 28),
        1967: (NOV, 17),
        1968: (NOV, 6),
        1969: (OCT, 26),
        1970: (OCT, 16),
        1971: (OCT, 5),
        1972: (SEP, 23),
        1973: (SEP, 12),
        1974: (SEP, 2),
        1975: (AUG, 22),
        1976: (AUG, 11),
        1977: (JUL, 31),
        1978: (JUL, 20),
        1979: (JUL, 9),
        1980: (JUN, 28),
        1981: (JUN, 17),
        1982: (JUN, 7),
        1983: (MAY, 27),
        1984: (MAY, 16),
        1985: (MAY, 5),
        1986: (APR, 24),
        1987: (APR, 13),
        1988: (APR, 2),
        1989: (MAR, 22),
        1990: (MAR, 12),
        1991: (MAR, 1),
        1992: (FEB, 18),
        1993: (FEB, 6),
        1994: (JAN, 26),
        1995: (JAN, 16),
        1996: ((JAN, 6), (DEC, 25)),
        1997: (DEC, 15),
        1998: (DEC, 4),
        1999: (NOV, 23),
        2000: (NOV, 11),
        2001: (OCT, 31),
        2002: (OCT, 21),
        2003: (OCT, 11),
        2004: (SEP, 29),
        2005: (SEP, 19),
        2006: (SEP, 8),
        2007: (AUG, 28),
        2008: (AUG, 16),
        2009: (AUG, 6),
        2010: (JUL, 27),
        2011: (JUL, 16),
        2012: (JUL, 5),
        2013: (JUN, 24),
        2014: (JUN, 13),
        2015: (JUN, 2),
        2016: (MAY, 22),
        2017: (MAY, 11),
        2018: (MAY, 1),
        2019: (APR, 20),
        2020: (APR, 8),
        2021: (MAR, 28),
        2022: (MAR, 18),
        2023: (MAR, 7),
        2024: (FEB, 25),
        2025: (FEB, 14),
        2026: (FEB, 3),
        2027: (JAN, 23),
        2028: ((JAN, 12), (DEC, 31)),
        2029: (DEC, 21),
        2030: (DEC, 10),
        2031: (NOV, 30),
        2032: (NOV, 18),
        2033: (NOV, 7),
        2034: (OCT, 27),
        2035: (OCT, 16),
        2036: (OCT, 5),
        2037: (SEP, 25),
        2038: (SEP, 14),
        2039: (SEP, 4),
        2040: (AUG, 23),
        2041: (AUG, 12),
        2042: (AUG, 1),
        2043: (JUL, 22),
        2044: (JUL, 10),
        2045: (JUN, 30),
        2046: (JUN, 19),
        2047: (JUN, 9),
        2048: (MAY, 28),
        2049: (MAY, 17),
        2050: (MAY, 6),
        2051: (APR, 26),
        2052: (APR, 15),
        2053: (APR, 4),
        2054: (MAR, 24),
        2055: (MAR, 13),
        2056: (MAR, 2),
        2057: (FEB, 19),
        2058: (FEB, 8),
        2059: (JAN, 29),
        2060: (JAN, 19),
        2061: ((JAN, 7), (DEC, 27)),
        2062: (DEC, 16),
        2063: (DEC, 6),
        2064: (NOV, 24),
        2065: (NOV, 14),
        2066: (NOV, 3),
        2067: (OCT, 24),
        2068: (OCT, 12),
        2069: (OCT, 1),
        2070: (SEP, 20),
        2071: (SEP, 9),
        2072: (AUG, 29),
        2073: (AUG, 19),
        2074: (AUG, 8),
        2075: (JUL, 28),
        2076: (JUL, 16),
        2077: (JUL, 5),
    }

    ISRA_AND_MIRAJ_DATES = {
        1925: (FEB, 21),
        1926: (FEB, 10),
        1927: (JAN, 31),
        1928: (JAN, 20),
        1929: ((JAN, 8), (DEC, 28)),
        1930: (DEC, 17),
        1931: (DEC, 7),
        1932: (NOV, 26),
        1933: (NOV, 15),
        1934: (NOV, 5),
        1935: (OCT, 25),
        1936: (OCT, 13),
        1937: (OCT, 2),
        1938: (SEP, 21),
        1939: (SEP, 11),
        1940: (AUG, 31),
        1941: (AUG, 19),
        1942: (AUG, 9),
        1943: (JUL, 29),
        1944: (JUL, 17),
        1945: (JUL, 7),
        1946: (JUN, 26),
        1947: (JUN, 16),
        1948: (JUN, 4),
        1949: (MAY, 24),
        1950: (MAY, 14),
        1951: (MAY, 4),
        1952: (APR, 22),
        1953: (APR, 12),
        1954: (APR, 1),
        1955: (MAR, 21),
        1956: (MAR, 10),
        1957: (FEB, 27),
        1958: (FEB, 16),
        1959: (FEB, 6),
        1960: (JAN, 26),
        1961: (JAN, 14),
        1962: ((JAN, 4), (DEC, 24)),
        1963: (DEC, 13),
        1964: (DEC, 1),
        1965: (NOV, 20),
        1966: (NOV, 10),
        1967: (OCT, 30),
        1968: (OCT, 19),
        1969: (OCT, 8),
        1970: (SEP, 28),
        1971: (SEP, 17),
        1972: (SEP, 5),
        1973: (AUG, 25),
        1974: (AUG, 15),
        1975: (AUG, 5),
        1976: (JUL, 24),
        1977: (JUL, 13),
        1978: (JUL, 2),
        1979: (JUN, 22),
        1980: (JUN, 10),
        1981: (MAY, 31),
        1982: (MAY, 20),
        1983: (MAY, 10),
        1984: (APR, 28),
        1985: (APR, 17),
        1986: (APR, 6),
        1987: (MAR, 27),
        1988: (MAR, 15),
        1989: (MAR, 5),
        1990: (FEB, 22),
        1991: (FEB, 11),
        1992: (JAN, 31),
        1993: (JAN, 20),
        1994: ((JAN, 9), (DEC, 29)),
        1995: (DEC, 19),
        1996: (DEC, 8),
        1997: (NOV, 27),
        1998: (NOV, 16),
        1999: (NOV, 5),
        2000: (OCT, 24),
        2001: (OCT, 14),
        2002: (OCT, 4),
        2003: (SEP, 24),
        2004: (SEP, 12),
        2005: (SEP, 1),
        2006: (AUG, 21),
        2007: (AUG, 10),
        2008: (JUL, 30),
        2009: (JUL, 20),
        2010: (JUL, 9),
        2011: (JUN, 29),
        2012: (JUN, 17),
        2013: (JUN, 6),
        2014: (MAY, 26),
        2015: (MAY, 16),
        2016: (MAY, 4),
        2017: (APR, 24),
        2018: (APR, 13),
        2019: (APR, 3),
        2020: (MAR, 22),
        2021: (MAR, 11),
        2022: (FEB, 28),
        2023: (FEB, 18),
        2024: (FEB, 8),
        2025: (JAN, 27),
        2026: (JAN, 16),
        2027: ((JAN, 5), (DEC, 25)),
        2028: (DEC, 14),
        2029: (DEC, 3),
        2030: (NOV, 23),
        2031: (NOV, 12),
        2032: (NOV, 1),
        2033: (OCT, 21),
        2034: (OCT, 10),
        2035: (SEP, 29),
        2036: (SEP, 18),
        2037: (SEP, 7),
        2038: (AUG, 28),
        2039: (AUG, 17),
        2040: (AUG, 5),
        2041: (JUL, 25),
        2042: (JUL, 15),
        2043: (JUL, 4),
        2044: (JUN, 23),
        2045: (JUN, 13),
        2046: (JUN, 2),
        2047: (MAY, 22),
        2048: (MAY, 10),
        2049: (APR, 29),
        2050: (APR, 19),
        2051: (APR, 9),
        2052: (MAR, 28),
        2053: (MAR, 18),
        2054: (MAR, 7),
        2055: (FEB, 24),
        2056: (FEB, 13),
        2057: (FEB, 1),
        2058: (JAN, 22),
        2059: (JAN, 12),
        2060: ((JAN, 1), (DEC, 20)),
        2061: (DEC, 9),
        2062: (NOV, 29),
        2063: (NOV, 18),
        2064: (NOV, 7),
        2065: (OCT, 27),
        2066: (OCT, 17),
        2067: (OCT, 6),
        2068: (SEP, 24),
        2069: (SEP, 13),
        2070: (SEP, 3),
        2071: (AUG, 23),
        2072: (AUG, 12),
        2073: (AUG, 1),
        2074: (JUL, 22),
        2075: (JUL, 11),
        2076: (JUN, 29),
        2077: (JUN, 18),
    }

    LAYLAT_AL_QADR_DATES = {
        1925: (APR, 22),
        1926: (APR, 10),
        1927: (MAR, 30),
        1928: (MAR, 19),
        1929: (MAR, 8),
        1930: (FEB, 26),
        1931: (FEB, 15),
        1932: (FEB, 4),
        1933: (JAN, 23),
        1934: (JAN, 13),
        1935: ((JAN, 3), (DEC, 23)),
        1936: (DEC, 11),
        1937: (DEC, 1),
        1938: (NOV, 19),
        1939: (NOV, 9),
        1940: (OCT, 29),
        1941: (OCT, 17),
        1942: (OCT, 7),
        1943: (SEP, 26),
        1944: (SEP, 14),
        1945: (SEP, 3),
        1946: (AUG, 24),
        1947: (AUG, 14),
        1948: (AUG, 2),
        1949: (JUL, 22),
        1950: (JUL, 13),
        1951: (JUL, 2),
        1952: (JUN, 20),
        1953: (JUN, 9),
        1954: (MAY, 30),
        1955: (MAY, 20),
        1956: (MAY, 8),
        1957: (APR, 27),
        1958: (APR, 16),
        1959: (APR, 6),
        1960: (MAR, 25),
        1961: (MAR, 14),
        1962: (MAR, 3),
        1963: (FEB, 21),
        1964: (FEB, 10),
        1965: (JAN, 29),
        1966: (JAN, 18),
        1967: ((JAN, 8), (DEC, 28)),
        1968: (DEC, 17),
        1969: (DEC, 6),
        1970: (NOV, 27),
        1971: (NOV, 15),
        1972: (NOV, 3),
        1973: (OCT, 23),
        1974: (OCT, 13),
        1975: (OCT, 2),
        1976: (SEP, 21),
        1977: (SEP, 10),
        1978: (AUG, 31),
        1979: (AUG, 20),
        1980: (AUG, 8),
        1981: (JUL, 28),
        1982: (JUL, 18),
        1983: (JUL, 8),
        1984: (JUN, 26),
        1985: (JUN, 15),
        1986: (JUN, 4),
        1987: (MAY, 25),
        1988: (MAY, 13),
        1989: (MAY, 3),
        1990: (APR, 22),
        1991: (APR, 12),
        1992: (MAR, 31),
        1993: (MAR, 20),
        1994: (MAR, 9),
        1995: (FEB, 26),
        1996: (FEB, 16),
        1997: (FEB, 5),
        1998: (JAN, 25),
        1999: (JAN, 14),
        2000: ((JAN, 4), (DEC, 23)),
        2001: (DEC, 12),
        2002: (DEC, 2),
        2003: (NOV, 21),
        2004: (NOV, 10),
        2005: (OCT, 30),
        2006: (OCT, 20),
        2007: (OCT, 9),
        2008: (SEP, 27),
        2009: (SEP, 17),
        2010: (SEP, 6),
        2011: (AUG, 27),
        2012: (AUG, 15),
        2013: (AUG, 4),
        2014: (JUL, 24),
        2015: (JUL, 14),
        2016: (JUL, 2),
        2017: (JUN, 22),
        2018: (JUN, 11),
        2019: (JUN, 1),
        2020: (MAY, 20),
        2021: (MAY, 9),
        2022: (APR, 28),
        2023: (APR, 18),
        2024: (APR, 6),
        2025: (MAR, 27),
        2026: (MAR, 16),
        2027: (MAR, 6),
        2028: (FEB, 23),
        2029: (FEB, 11),
        2030: (JAN, 31),
        2031: (JAN, 21),
        2032: ((JAN, 10), (DEC, 30)),
        2033: (DEC, 19),
        2034: (DEC, 8),
        2035: (NOV, 27),
        2036: (NOV, 15),
        2037: (NOV, 5),
        2038: (OCT, 26),
        2039: (OCT, 15),
        2040: (OCT, 3),
        2041: (SEP, 23),
        2042: (SEP, 12),
        2043: (SEP, 1),
        2044: (AUG, 21),
        2045: (AUG, 10),
        2046: (JUL, 31),
        2047: (JUL, 20),
        2048: (JUL, 8),
        2049: (JUN, 28),
        2050: (JUN, 17),
        2051: (JUN, 6),
        2052: (MAY, 26),
        2053: (MAY, 16),
        2054: (MAY, 5),
        2055: (APR, 24),
        2056: (APR, 12),
        2057: (APR, 1),
        2058: (MAR, 22),
        2059: (MAR, 12),
        2060: (FEB, 29),
        2061: (FEB, 18),
        2062: (FEB, 7),
        2063: (JAN, 27),
        2064: (JAN, 16),
        2065: ((JAN, 4), (DEC, 25)),
        2066: (DEC, 15),
        2067: (DEC, 4),
        2068: (NOV, 22),
        2069: (NOV, 11),
        2070: (OCT, 31),
        2071: (OCT, 21),
        2072: (OCT, 9),
        2073: (SEP, 29),
        2074: (SEP, 18),
        2075: (SEP, 8),
        2076: (AUG, 27),
        2077: (AUG, 16),
    }

    MALDIVES_EMBRACED_ISLAM_DAY_DATES = {
        1924: (OCT, 29),
        1925: (OCT, 19),
        1926: (OCT, 8),
        1927: (SEP, 27),
        1928: (SEP, 15),
        1929: (SEP, 5),
        1930: (AUG, 25),
        1931: (AUG, 15),
        1932: (AUG, 4),
        1933: (JUL, 24),
        1934: (JUL, 13),
        1935: (JUL, 2),
        1936: (JUN, 21),
        1937: (JUN, 10),
        1938: (MAY, 30),
        1939: (MAY, 20),
        1940: (MAY, 9),
        1941: (APR, 27),
        1942: (APR, 17),
        1943: (APR, 6),
        1944: (MAR, 25),
        1945: (MAR, 15),
        1946: (MAR, 4),
        1947: (FEB, 22),
        1948: (FEB, 11),
        1949: (JAN, 30),
        1950: (JAN, 20),
        1951: ((JAN, 10), (DEC, 30)),
        1952: (DEC, 18),
        1953: (DEC, 7),
        1954: (NOV, 27),
        1955: (NOV, 17),
        1956: (NOV, 4),
        1957: (OCT, 24),
        1958: (OCT, 15),
        1959: (OCT, 3),
        1960: (SEP, 22),
        1961: (SEP, 11),
        1962: (AUG, 31),
        1963: (AUG, 20),
        1964: (AUG, 9),
        1965: (JUL, 29),
        1966: (JUL, 19),
        1967: (JUL, 8),
        1968: (JUN, 27),
        1969: (JUN, 16),
        1970: (JUN, 6),
        1971: (MAY, 26),
        1972: (MAY, 14),
        1973: (MAY, 4),
        1974: (APR, 23),
        1975: (APR, 12),
        1976: (MAR, 31),
        1977: (MAR, 20),
        1978: (MAR, 10),
        1979: (FEB, 27),
        1980: (FEB, 17),
        1981: (FEB, 5),
        1982: (JAN, 26),
        1983: (JAN, 15),
        1984: ((JAN, 4), (DEC, 23)),
        1985: (DEC, 13),
        1986: (DEC, 2),
        1987: (NOV, 22),
        1988: (NOV, 10),
        1989: (OCT, 30),
        1990: (OCT, 19),
        1991: (OCT, 8),
        1992: (SEP, 27),
        1993: (SEP, 17),
        1994: (SEP, 6),
        1995: (AUG, 27),
        1996: (AUG, 15),
        1997: (AUG, 4),
        1998: (JUL, 24),
        1999: (JUL, 14),
        2000: (JUL, 3),
        2001: (JUN, 22),
        2002: (JUN, 12),
        2003: (JUN, 1),
        2004: (MAY, 20),
        2005: (MAY, 9),
        2006: (APR, 29),
        2007: (APR, 18),
        2008: (APR, 7),
        2009: (MAR, 28),
        2010: (MAR, 17),
        2011: (MAR, 6),
        2012: (FEB, 23),
        2013: (FEB, 11),
        2014: (FEB, 1),
        2015: (JAN, 21),
        2016: ((JAN, 11), (DEC, 30)),
        2017: (DEC, 19),
        2018: (DEC, 8),
        2019: (NOV, 28),
        2020: (NOV, 16),
        2021: (NOV, 6),
        2022: (OCT, 26),
        2023: (OCT, 16),
        2024: (OCT, 4),
        2025: (SEP, 23),
        2026: (SEP, 12),
        2027: (SEP, 2),
        2028: (AUG, 22),
        2029: (AUG, 11),
        2030: (AUG, 1),
        2031: (JUL, 21),
        2032: (JUL, 9),
        2033: (JUN, 28),
        2034: (JUN, 17),
        2035: (JUN, 7),
        2036: (MAY, 27),
        2037: (MAY, 16),
        2038: (MAY, 5),
        2039: (APR, 24),
        2040: (APR, 13),
        2041: (APR, 2),
        2042: (MAR, 23),
        2043: (MAR, 12),
        2044: (MAR, 1),
        2045: (FEB, 18),
        2046: (FEB, 7),
        2047: (JAN, 27),
        2048: (JAN, 16),
        2049: ((JAN, 5), (DEC, 26)),
        2050: (DEC, 15),
        2051: (DEC, 4),
        2052: (NOV, 22),
        2053: (NOV, 11),
        2054: (NOV, 1),
        2055: (OCT, 21),
        2056: (OCT, 10),
        2057: (SEP, 30),
        2058: (SEP, 19),
        2059: (SEP, 8),
        2060: (AUG, 27),
        2061: (AUG, 16),
        2062: (AUG, 6),
        2063: (JUL, 27),
        2064: (JUL, 15),
        2065: (JUL, 5),
        2066: (JUN, 24),
        2067: (JUN, 13),
        2068: (JUN, 1),
        2069: (MAY, 22),
        2070: (MAY, 11),
        2071: (MAY, 1),
        2072: (APR, 19),
        2073: (APR, 9),
        2074: (MAR, 29),
        2075: (MAR, 18),
        2076: (MAR, 6),
        2077: (FEB, 24),
    }

    MAWLID_DATES = {
        1924: (OCT, 10),
        1925: (SEP, 30),
        1926: (SEP, 19),
        1927: (SEP, 8),
        1928: (AUG, 27),
        1929: (AUG, 17),
        1930: (AUG, 6),
        1931: (JUL, 28),
        1932: (JUL, 16),
        1933: (JUL, 5),
        1934: (JUN, 24),
        1935: (JUN, 14),
        1936: (JUN, 2),
        1937: (MAY, 22),
        1938: (MAY, 11),
        1939: (MAY, 2),
        1940: (APR, 20),
        1941: (APR, 8),
        1942: (MAR, 29),
        1943: (MAR, 18),
        1944: (MAR, 6),
        1945: (FEB, 24),
        1946: (FEB, 13),
        1947: (FEB, 3),
        1948: (JAN, 23),
        1949: (JAN, 11),
        1950: ((JAN, 1), (DEC, 22)),
        1951: (DEC, 11),
        1952: (NOV, 30),
        1953: (NOV, 19),
        1954: (NOV, 8),
        1955: (OCT, 29),
        1956: (OCT, 17),
        1957: (OCT, 6),
        1958: (SEP, 26),
        1959: (SEP, 15),
        1960: (SEP, 3),
        1961: (AUG, 23),
        1962: (AUG, 12),
        1963: (AUG, 2),
        1964: (JUL, 21),
        1965: (JUL, 10),
        1966: (JUL, 1),
        1967: (JUN, 19),
        1968: (JUN, 8),
        1969: (MAY, 28),
        1970: (MAY, 18),
        1971: (MAY, 7),
        1972: (APR, 25),
        1973: (APR, 15),
        1974: (APR, 4),
        1975: (MAR, 24),
        1976: (MAR, 12),
        1977: (MAR, 2),
        1978: (FEB, 19),
        1979: (FEB, 9),
        1980: (JAN, 30),
        1981: (JAN, 18),
        1982: ((JAN, 7), (DEC, 27)),
        1983: (DEC, 16),
        1984: (DEC, 4),
        1985: (NOV, 24),
        1986: (NOV, 14),
        1987: (NOV, 3),
        1988: (OCT, 22),
        1989: (OCT, 11),
        1990: (OCT, 1),
        1991: (SEP, 20),
        1992: (SEP, 9),
        1993: (AUG, 29),
        1994: (AUG, 19),
        1995: (AUG, 8),
        1996: (JUL, 27),
        1997: (JUL, 16),
        1998: (JUL, 6),
        1999: (JUN, 26),
        2000: (JUN, 14),
        2001: (JUN, 4),
        2002: (MAY, 24),
        2003: (MAY, 13),
        2004: (MAY, 1),
        2005: (APR, 21),
        2006: (APR, 10),
        2007: (MAR, 31),
        2008: (MAR, 20),
        2009: (MAR, 9),
        2010: (FEB, 26),
        2011: (FEB, 15),
        2012: (FEB, 4),
        2013: (JAN, 24),
        2014: (JAN, 13),
        2015: ((JAN, 3), (DEC, 23)),
        2016: (DEC, 11),
        2017: (NOV, 30),
        2018: (NOV, 20),
        2019: (NOV, 9),
        2020: (OCT, 29),
        2021: (OCT, 18),
        2022: (OCT, 8),
        2023: (SEP, 27),
        2024: (SEP, 15),
        2025: (SEP, 4),
        2026: (AUG, 25),
        2027: (AUG, 14),
        2028: (AUG, 3),
        2029: (JUL, 24),
        2030: (JUL, 13),
        2031: (JUL, 2),
        2032: (JUN, 20),
        2033: (JUN, 9),
        2034: (MAY, 30),
        2035: (MAY, 20),
        2036: (MAY, 8),
        2037: (APR, 28),
        2038: (APR, 17),
        2039: (APR, 6),
        2040: (MAR, 25),
        2041: (MAR, 15),
        2042: (MAR, 4),
        2043: (FEB, 22),
        2044: (FEB, 11),
        2045: (JAN, 30),
        2046: (JAN, 19),
        2047: ((JAN, 8), (DEC, 29)),
        2048: (DEC, 18),
        2049: (DEC, 7),
        2050: (NOV, 26),
        2051: (NOV, 16),
        2052: (NOV, 4),
        2053: (OCT, 24),
        2054: (OCT, 13),
        2055: (OCT, 3),
        2056: (SEP, 22),
        2057: (SEP, 11),
        2058: (AUG, 31),
        2059: (AUG, 20),
        2060: (AUG, 8),
        2061: (JUL, 29),
        2062: (JUL, 19),
        2063: (JUL, 8),
        2064: (JUN, 27),
        2065: (JUN, 16),
        2066: (JUN, 5),
        2067: (MAY, 25),
        2068: (MAY, 14),
        2069: (MAY, 3),
        2070: (APR, 23),
        2071: (APR, 13),
        2072: (APR, 1),
        2073: (MAR, 21),
        2074: (MAR, 10),
        2075: (FEB, 27),
        2076: (FEB, 17),
        2077: (FEB, 6),
    }

    NUZUL_AL_QURAN_DATES = {
        1925: (APR, 12),
        1926: (MAR, 31),
        1927: (MAR, 20),
        1928: (MAR, 9),
        1929: (FEB, 26),
        1930: (FEB, 16),
        1931: (FEB, 5),
        1932: (JAN, 25),
        1933: (JAN, 13),
        1934: ((JAN, 3), (DEC, 24)),
        1935: (DEC, 13),
        1936: (DEC, 1),
        1937: (NOV, 21),
        1938: (NOV, 9),
        1939: (OCT, 30),
        1940: (OCT, 19),
        1941: (OCT, 7),
        1942: (SEP, 27),
        1943: (SEP, 16),
        1944: (SEP, 4),
        1945: (AUG, 24),
        1946: (AUG, 14),
        1947: (AUG, 4),
        1948: (JUL, 23),
        1949: (JUL, 12),
        1950: (JUL, 3),
        1951: (JUN, 22),
        1952: (JUN, 10),
        1953: (MAY, 30),
        1954: (MAY, 20),
        1955: (MAY, 10),
        1956: (APR, 28),
        1957: (APR, 17),
        1958: (APR, 6),
        1959: (MAR, 27),
        1960: (MAR, 15),
        1961: (MAR, 4),
        1962: (FEB, 21),
        1963: (FEB, 11),
        1964: (JAN, 31),
        1965: (JAN, 19),
        1966: ((JAN, 8), (DEC, 29)),
        1967: (DEC, 18),
        1968: (DEC, 7),
        1969: (NOV, 26),
        1970: (NOV, 17),
        1971: (NOV, 5),
        1972: (OCT, 24),
        1973: (OCT, 13),
        1974: (OCT, 3),
        1975: (SEP, 22),
        1976: (SEP, 11),
        1977: (AUG, 31),
        1978: (AUG, 21),
        1979: (AUG, 10),
        1980: (JUL, 29),
        1981: (JUL, 18),
        1982: (JUL, 8),
        1983: (JUN, 28),
        1984: (JUN, 16),
        1985: (JUN, 5),
        1986: (MAY, 25),
        1987: (MAY, 15),
        1988: (MAY, 3),
        1989: (APR, 23),
        1990: (APR, 12),
        1991: (APR, 2),
        1992: (MAR, 21),
        1993: (MAR, 10),
        1994: (FEB, 27),
        1995: (FEB, 16),
        1996: (FEB, 6),
        1997: (JAN, 26),
        1998: (JAN, 15),
        1999: ((JAN, 4), (DEC, 25)),
        2000: (DEC, 13),
        2001: (DEC, 2),
        2002: (NOV, 22),
        2003: (NOV, 11),
        2004: (OCT, 31),
        2005: (OCT, 20),
        2006: (OCT, 10),
        2007: (SEP, 29),
        2008: (SEP, 17),
        2009: (SEP, 7),
        2010: (AUG, 27),
        2011: (AUG, 17),
        2012: (AUG, 5),
        2013: (JUL, 25),
        2014: (JUL, 14),
        2015: (JUL, 4),
        2016: (JUN, 22),
        2017: (JUN, 12),
        2018: (JUN, 1),
        2019: (MAY, 22),
        2020: (MAY, 10),
        2021: (APR, 29),
        2022: (APR, 18),
        2023: (APR, 8),
        2024: (MAR, 27),
        2025: (MAR, 17),
        2026: (MAR, 6),
        2027: (FEB, 24),
        2028: (FEB, 13),
        2029: (FEB, 1),
        2030: (JAN, 21),
        2031: ((JAN, 11), (DEC, 31)),
        2032: (DEC, 20),
        2033: (DEC, 9),
        2034: (NOV, 28),
        2035: (NOV, 17),
        2036: (NOV, 5),
        2037: (OCT, 26),
        2038: (OCT, 16),
        2039: (OCT, 5),
        2040: (SEP, 23),
        2041: (SEP, 13),
        2042: (SEP, 2),
        2043: (AUG, 22),
        2044: (AUG, 11),
        2045: (JUL, 31),
        2046: (JUL, 21),
        2047: (JUL, 10),
        2048: (JUN, 28),
        2049: (JUN, 18),
        2050: (JUN, 7),
        2051: (MAY, 27),
        2052: (MAY, 16),
        2053: (MAY, 6),
        2054: (APR, 25),
        2055: (APR, 14),
        2056: (APR, 2),
        2057: (MAR, 22),
        2058: (MAR, 12),
        2059: (MAR, 2),
        2060: (FEB, 19),
        2061: (FEB, 8),
        2062: (JAN, 28),
        2063: (JAN, 17),
        2064: ((JAN, 6), (DEC, 25)),
        2065: (DEC, 15),
        2066: (DEC, 5),
        2067: (NOV, 24),
        2068: (NOV, 12),
        2069: (NOV, 1),
        2070: (OCT, 21),
        2071: (OCT, 11),
        2072: (SEP, 29),
        2073: (SEP, 19),
        2074: (SEP, 8),
        2075: (AUG, 29),
        2076: (AUG, 17),
        2077: (AUG, 6),
    }

    PROPHET_DEATH_DATES = {
        1924: (SEP, 27),
        1925: (SEP, 17),
        1926: (SEP, 6),
        1927: (AUG, 26),
        1928: (AUG, 14),
        1929: (AUG, 4),
        1930: (JUL, 24),
        1931: (JUL, 14),
        1932: (JUL, 3),
        1933: (JUN, 22),
        1934: (JUN, 11),
        1935: (MAY, 31),
        1936: (MAY, 20),
        1937: (MAY, 9),
        1938: (APR, 28),
        1939: (APR, 18),
        1940: (APR, 6),
        1941: (MAR, 26),
        1942: (MAR, 16),
        1943: (MAR, 5),
        1944: (FEB, 22),
        1945: (FEB, 11),
        1946: (JAN, 31),
        1947: (JAN, 21),
        1948: ((JAN, 10), (DEC, 29)),
        1949: (DEC, 19),
        1950: (DEC, 9),
        1951: (NOV, 28),
        1952: (NOV, 16),
        1953: (NOV, 5),
        1954: (OCT, 26),
        1955: (OCT, 16),
        1956: (OCT, 3),
        1957: (SEP, 23),
        1958: (SEP, 13),
        1959: (SEP, 1),
        1960: (AUG, 21),
        1961: (AUG, 10),
        1962: (JUL, 30),
        1963: (JUL, 19),
        1964: (JUL, 8),
        1965: (JUN, 27),
        1966: (JUN, 17),
        1967: (JUN, 6),
        1968: (MAY, 25),
        1969: (MAY, 15),
        1970: (MAY, 5),
        1971: (APR, 24),
        1972: (APR, 12),
        1973: (APR, 2),
        1974: (MAR, 22),
        1975: (MAR, 11),
        1976: (FEB, 28),
        1977: (FEB, 16),
        1978: (FEB, 6),
        1979: (JAN, 26),
        1980: (JAN, 16),
        1981: ((JAN, 4), (DEC, 24)),
        1982: (DEC, 13),
        1983: (DEC, 2),
        1984: (NOV, 21),
        1985: (NOV, 11),
        1986: (OCT, 31),
        1987: (OCT, 21),
        1988: (OCT, 9),
        1989: (SEP, 28),
        1990: (SEP, 17),
        1991: (SEP, 7),
        1992: (AUG, 26),
        1993: (AUG, 16),
        1994: (AUG, 5),
        1995: (JUL, 26),
        1996: (JUL, 14),
        1997: (JUL, 3),
        1998: (JUN, 22),
        1999: (JUN, 12),
        2000: (JUN, 1),
        2001: (MAY, 22),
        2002: (MAY, 11),
        2003: (APR, 30),
        2004: (APR, 18),
        2005: (APR, 7),
        2006: (MAR, 28),
        2007: (MAR, 18),
        2008: (MAR, 6),
        2009: (FEB, 23),
        2010: (FEB, 12),
        2011: (FEB, 1),
        2012: (JAN, 22),
        2013: ((JAN, 10), (DEC, 31)),
        2014: (DEC, 20),
        2015: (DEC, 10),
        2016: (NOV, 28),
        2017: (NOV, 17),
        2018: (NOV, 6),
        2019: (OCT, 27),
        2020: (OCT, 15),
        2021: (OCT, 5),
        2022: (SEP, 24),
        2023: (SEP, 13),
        2024: (SEP, 1),
        2025: (AUG, 22),
        2026: (AUG, 11),
        2027: (AUG, 1),
        2028: (JUL, 21),
        2029: (JUL, 10),
        2030: (JUN, 29),
        2031: (JUN, 18),
        2032: (JUN, 6),
        2033: (MAY, 27),
        2034: (MAY, 17),
        2035: (MAY, 6),
        2036: (APR, 25),
        2037: (APR, 14),
        2038: (APR, 3),
        2039: (MAR, 23),
        2040: (MAR, 12),
        2041: (MAR, 1),
        2042: (FEB, 19),
        2043: (FEB, 8),
        2044: (JAN, 29),
        2045: (JAN, 17),
        2046: ((JAN, 6), (DEC, 26)),
        2047: (DEC, 16),
        2048: (DEC, 4),
        2049: (NOV, 24),
        2050: (NOV, 13),
        2051: (NOV, 2),
        2052: (OCT, 21),
        2053: (OCT, 10),
        2054: (SEP, 30),
        2055: (SEP, 20),
        2056: (SEP, 8),
        2057: (AUG, 28),
        2058: (AUG, 17),
        2059: (AUG, 7),
        2060: (JUL, 26),
        2061: (JUL, 16),
        2062: (JUL, 5),
        2063: (JUN, 25),
        2064: (JUN, 13),
        2065: (JUN, 2),
        2066: (MAY, 22),
        2067: (MAY, 12),
        2068: (APR, 30),
        2069: (APR, 20),
        2070: (APR, 10),
        2071: (MAR, 30),
        2072: (MAR, 18),
        2073: (MAR, 7),
        2074: (FEB, 24),
        2075: (FEB, 14),
        2076: (FEB, 3),
        2077: (JAN, 23),
    }

    QUAMEE_DHUVAS_DATES = {
        1924: (SEP, 29),
        1925: (SEP, 19),
        1926: (SEP, 8),
        1927: (AUG, 28),
        1928: (AUG, 16),
        1929: (AUG, 6),
        1930: (JUL, 26),
        1931: (JUL, 17),
        1932: (JUL, 5),
        1933: (JUN, 24),
        1934: (JUN, 13),
        1935: (JUN, 3),
        1936: (MAY, 22),
        1937: (MAY, 11),
        1938: (APR, 30),
        1939: (APR, 21),
        1940: (APR, 9),
        1941: (MAR, 28),
        1942: (MAR, 18),
        1943: (MAR, 7),
        1944: (FEB, 24),
        1945: (FEB, 13),
        1946: (FEB, 2),
        1947: (JAN, 23),
        1948: ((JAN, 12), (DEC, 31)),
        1949: (DEC, 21),
        1950: (DEC, 11),
        1951: (NOV, 30),
        1952: (NOV, 19),
        1953: (NOV, 8),
        1954: (OCT, 28),
        1955: (OCT, 18),
        1956: (OCT, 6),
        1957: (SEP, 25),
        1958: (SEP, 15),
        1959: (SEP, 4),
        1960: (AUG, 23),
        1961: (AUG, 12),
        1962: (AUG, 1),
        1963: (JUL, 22),
        1964: (JUL, 10),
        1965: (JUN, 29),
        1966: (JUN, 20),
        1967: (JUN, 8),
        1968: (MAY, 28),
        1969: (MAY, 17),
        1970: (MAY, 7),
        1971: (APR, 26),
        1972: (APR, 14),
        1973: (APR, 4),
        1974: (MAR, 24),
        1975: (MAR, 13),
        1976: (MAR, 1),
        1977: (FEB, 19),
        1978: (FEB, 8),
        1979: (JAN, 29),
        1980: (JAN, 19),
        1981: ((JAN, 7), (DEC, 27)),
        1982: (DEC, 16),
        1983: (DEC, 5),
        1984: (NOV, 23),
        1985: (NOV, 13),
        1986: (NOV, 3),
        1987: (OCT, 23),
        1988: (OCT, 11),
        1989: (SEP, 30),
        1990: (SEP, 20),
        1991: (SEP, 9),
        1992: (AUG, 29),
        1993: (AUG, 18),
        1994: (AUG, 8),
        1995: (JUL, 28),
        1996: (JUL, 16),
        1997: (JUL, 5),
        1998: (JUN, 25),
        1999: (JUN, 15),
        2000: (JUN, 3),
        2001: (MAY, 24),
        2002: (MAY, 13),
        2003: (MAY, 2),
        2004: (APR, 20),
        2005: (APR, 10),
        2006: (MAR, 30),
        2007: (MAR, 20),
        2008: (MAR, 9),
        2009: (FEB, 26),
        2010: (FEB, 15),
        2011: (FEB, 4),
        2012: (JAN, 24),
        2013: (JAN, 13),
        2014: ((JAN, 2), (DEC, 23)),
        2015: (DEC, 12),
        2016: (NOV, 30),
        2017: (NOV, 19),
        2018: (NOV, 9),
        2019: (OCT, 29),
        2020: (OCT, 18),
        2021: (OCT, 7),
        2022: (SEP, 27),
        2023: (SEP, 16),
        2024: (SEP, 4),
        2025: (AUG, 24),
        2026: (AUG, 14),
        2027: (AUG, 3),
        2028: (JUL, 23),
        2029: (JUL, 13),
        2030: (JUL, 2),
        2031: (JUN, 21),
        2032: (JUN, 9),
        2033: (MAY, 29),
        2034: (MAY, 19),
        2035: (MAY, 9),
        2036: (APR, 27),
        2037: (APR, 17),
        2038: (APR, 6),
        2039: (MAR, 26),
        2040: (MAR, 14),
        2041: (MAR, 4),
        2042: (FEB, 21),
        2043: (FEB, 11),
        2044: (JAN, 31),
        2045: (JAN, 19),
        2046: ((JAN, 8), (DEC, 28)),
        2047: (DEC, 18),
        2048: (DEC, 7),
        2049: (NOV, 26),
        2050: (NOV, 15),
        2051: (NOV, 5),
        2052: (OCT, 24),
        2053: (OCT, 13),
        2054: (OCT, 2),
        2055: (SEP, 22),
        2056: (SEP, 11),
        2057: (AUG, 31),
        2058: (AUG, 20),
        2059: (AUG, 9),
        2060: (JUL, 28),
        2061: (JUL, 18),
        2062: (JUL, 8),
        2063: (JUN, 27),
        2064: (JUN, 16),
        2065: (JUN, 5),
        2066: (MAY, 25),
        2067: (MAY, 14),
        2068: (MAY, 3),
        2069: (APR, 22),
        2070: (APR, 12),
        2071: (APR, 2),
        2072: (MAR, 21),
        2073: (MAR, 10),
        2074: (FEB, 27),
        2075: (FEB, 16),
        2076: (FEB, 6),
        2077: (JAN, 26),
    }

    RAMADAN_BEGINNING_DATES = {
        1925: (MAR, 27),
        1926: (MAR, 15),
        1927: (MAR, 4),
        1928: (FEB, 22),
        1929: (FEB, 10),
        1930: (JAN, 31),
        1931: (JAN, 20),
        1932: ((JAN, 9), (DEC, 28)),
        1933: (DEC, 18),
        1934: (DEC, 8),
        1935: (NOV, 27),
        1936: (NOV, 15),
        1937: (NOV, 5),
        1938: (OCT, 24),
        1939: (OCT, 14),
        1940: (OCT, 3),
        1941: (SEP, 21),
        1942: (SEP, 11),
        1943: (AUG, 31),
        1944: (AUG, 19),
        1945: (AUG, 8),
        1946: (JUL, 29),
        1947: (JUL, 19),
        1948: (JUL, 7),
        1949: (JUN, 26),
        1950: (JUN, 17),
        1951: (JUN, 6),
        1952: (MAY, 25),
        1953: (MAY, 14),
        1954: (MAY, 4),
        1955: (APR, 24),
        1956: (APR, 12),
        1957: (APR, 1),
        1958: (MAR, 21),
        1959: (MAR, 11),
        1960: (FEB, 28),
        1961: (FEB, 16),
        1962: (FEB, 5),
        1963: (JAN, 26),
        1964: (JAN, 15),
        1965: ((JAN, 3), (DEC, 23)),
        1966: (DEC, 13),
        1967: (DEC, 2),
        1968: (NOV, 21),
        1969: (NOV, 10),
        1970: (NOV, 1),
        1971: (OCT, 20),
        1972: (OCT, 8),
        1973: (SEP, 27),
        1974: (SEP, 17),
        1975: (SEP, 6),
        1976: (AUG, 26),
        1977: (AUG, 15),
        1978: (AUG, 5),
        1979: (JUL, 25),
        1980: (JUL, 13),
        1981: (JUL, 2),
        1982: (JUN, 22),
        1983: (JUN, 12),
        1984: (MAY, 31),
        1985: (MAY, 20),
        1986: (MAY, 9),
        1987: (APR, 29),
        1988: (APR, 17),
        1989: (APR, 7),
        1990: (MAR, 27),
        1991: (MAR, 17),
        1992: (MAR, 5),
        1993: (FEB, 22),
        1994: (FEB, 11),
        1995: (JAN, 31),
        1996: (JAN, 21),
        1997: ((JAN, 10), (DEC, 30)),
        1998: (DEC, 19),
        1999: (DEC, 9),
        2000: (NOV, 27),
        2001: (NOV, 16),
        2002: (NOV, 6),
        2003: (OCT, 26),
        2004: (OCT, 15),
        2005: (OCT, 4),
        2006: (SEP, 24),
        2007: (SEP, 13),
        2008: (SEP, 1),
        2009: (AUG, 22),
        2010: (AUG, 11),
        2011: (AUG, 1),
        2012: (JUL, 20),
        2013: (JUL, 9),
        2014: (JUN, 28),
        2015: (JUN, 18),
        2016: (JUN, 6),
        2017: (MAY, 27),
        2018: (MAY, 16),
        2019: (MAY, 6),
        2020: (APR, 24),
        2021: (APR, 13),
        2022: (APR, 2),
        2023: (MAR, 23),
        2024: (MAR, 11),
        2025: (MAR, 1),
        2026: (FEB, 18),
        2027: (FEB, 8),
        2028: (JAN, 28),
        2029: (JAN, 16),
        2030: ((JAN, 5), (DEC, 26)),
        2031: (DEC, 15),
        2032: (DEC, 4),
        2033: (NOV, 23),
        2034: (NOV, 12),
        2035: (NOV, 1),
        2036: (OCT, 20),
        2037: (OCT, 10),
        2038: (SEP, 30),
        2039: (SEP, 19),
        2040: (SEP, 7),
        2041: (AUG, 28),
        2042: (AUG, 17),
        2043: (AUG, 6),
        2044: (JUL, 26),
        2045: (JUL, 15),
        2046: (JUL, 5),
        2047: (JUN, 24),
        2048: (JUN, 12),
        2049: (JUN, 2),
        2050: (MAY, 22),
        2051: (MAY, 11),
        2052: (APR, 30),
        2053: (APR, 20),
        2054: (APR, 9),
        2055: (MAR, 29),
        2056: (MAR, 17),
        2057: (MAR, 6),
        2058: (FEB, 24),
        2059: (FEB, 14),
        2060: (FEB, 3),
        2061: (JAN, 23),
        2062: (JAN, 12),
        2063: ((JAN, 1), (DEC, 21)),
        2064: (DEC, 9),
        2065: (NOV, 29),
        2066: (NOV, 19),
        2067: (NOV, 8),
        2068: (OCT, 27),
        2069: (OCT, 16),
        2070: (OCT, 5),
        2071: (SEP, 25),
        2072: (SEP, 13),
        2073: (SEP, 3),
        2074: (AUG, 23),
        2075: (AUG, 13),
        2076: (AUG, 1),
        2077: (JUL, 21),
    }

    SADIQ_BIRTHDAY_DATES = {
        1924: (OCT, 15),
        1925: (OCT, 5),
        1926: (SEP, 24),
        1927: (SEP, 13),
        1928: (SEP, 1),
        1929: (AUG, 22),
        1930: (AUG, 11),
        1931: (AUG, 2),
        1932: (JUL, 21),
        1933: (JUL, 10),
        1934: (JUN, 29),
        1935: (JUN, 19),
        1936: (JUN, 7),
        1937: (MAY, 27),
        1938: (MAY, 16),
        1939: (MAY, 7),
        1940: (APR, 25),
        1941: (APR, 13),
        1942: (APR, 3),
        1943: (MAR, 23),
        1944: (MAR, 11),
        1945: (MAR, 1),
        1946: (FEB, 18),
        1947: (FEB, 8),
        1948: (JAN, 28),
        1949: (JAN, 16),
        1950: ((JAN, 6), (DEC, 27)),
        1951: (DEC, 16),
        1952: (DEC, 5),
        1953: (NOV, 24),
        1954: (NOV, 13),
        1955: (NOV, 3),
        1956: (OCT, 22),
        1957: (OCT, 11),
        1958: (OCT, 1),
        1959: (SEP, 20),
        1960: (SEP, 8),
        1961: (AUG, 28),
        1962: (AUG, 17),
        1963: (AUG, 7),
        1964: (JUL, 26),
        1965: (JUL, 15),
        1966: (JUL, 6),
        1967: (JUN, 24),
        1968: (JUN, 13),
        1969: (JUN, 2),
        1970: (MAY, 23),
        1971: (MAY, 12),
        1972: (APR, 30),
        1973: (APR, 20),
        1974: (APR, 9),
        1975: (MAR, 29),
        1976: (MAR, 17),
        1977: (MAR, 7),
        1978: (FEB, 24),
        1979: (FEB, 14),
        1980: (FEB, 4),
        1981: (JAN, 23),
        1982: (JAN, 12),
        1983: ((JAN, 1), (DEC, 21)),
        1984: (DEC, 9),
        1985: (NOV, 29),
        1986: (NOV, 19),
        1987: (NOV, 8),
        1988: (OCT, 27),
        1989: (OCT, 16),
        1990: (OCT, 6),
        1991: (SEP, 25),
        1992: (SEP, 14),
        1993: (SEP, 3),
        1994: (AUG, 24),
        1995: (AUG, 13),
        1996: (AUG, 1),
        1997: (JUL, 21),
        1998: (JUL, 11),
        1999: (JUL, 1),
        2000: (JUN, 19),
        2001: (JUN, 9),
        2002: (MAY, 29),
        2003: (MAY, 18),
        2004: (MAY, 6),
        2005: (APR, 26),
        2006: (APR, 15),
        2007: (APR, 5),
        2008: (MAR, 25),
        2009: (MAR, 14),
        2010: (MAR, 3),
        2011: (FEB, 20),
        2012: (FEB, 9),
        2013: (JAN, 29),
        2014: (JAN, 18),
        2015: ((JAN, 8), (DEC, 28)),
        2016: (DEC, 16),
        2017: (DEC, 5),
        2018: (NOV, 25),
        2019: (NOV, 14),
        2020: (NOV, 3),
        2021: (OCT, 23),
        2022: (OCT, 13),
        2023: (OCT, 2),
        2024: (SEP, 20),
        2025: (SEP, 9),
        2026: (AUG, 30),
        2027: (AUG, 19),
        2028: (AUG, 8),
        2029: (JUL, 29),
        2030: (JUL, 18),
        2031: (JUL, 7),
        2032: (JUN, 25),
        2033: (JUN, 14),
        2034: (JUN, 4),
        2035: (MAY, 25),
        2036: (MAY, 13),
        2037: (MAY, 3),
        2038: (APR, 22),
        2039: (APR, 11),
        2040: (MAR, 30),
        2041: (MAR, 20),
        2042: (MAR, 9),
        2043: (FEB, 27),
        2044: (FEB, 16),
        2045: (FEB, 4),
        2046: (JAN, 24),
        2047: (JAN, 13),
        2048: ((JAN, 3), (DEC, 23)),
        2049: (DEC, 12),
        2050: (DEC, 1),
        2051: (NOV, 21),
        2052: (NOV, 9),
        2053: (OCT, 29),
        2054: (OCT, 18),
        2055: (OCT, 8),
        2056: (SEP, 27),
        2057: (SEP, 16),
        2058: (SEP, 5),
        2059: (AUG, 25),
        2060: (AUG, 13),
        2061: (AUG, 3),
        2062: (JUL, 24),
        2063: (JUL, 13),
        2064: (JUL, 2),
        2065: (JUN, 21),
        2066: (JUN, 10),
        2067: (MAY, 30),
        2068: (MAY, 19),
        2069: (MAY, 8),
        2070: (APR, 28),
        2071: (APR, 18),
        2072: (APR, 6),
        2073: (MAR, 26),
        2074: (MAR, 15),
        2075: (MAR, 4),
        2076: (FEB, 22),
        2077: (FEB, 11),
    }

    SADIQ_DEATH_DATES = {
        1925: (MAY, 18),
        1926: (MAY, 8),
        1927: (APR, 27),
        1928: (APR, 15),
        1929: (APR, 5),
        1930: (MAR, 25),
        1931: (MAR, 15),
        1932: (MAR, 3),
        1933: (FEB, 20),
        1934: (FEB, 10),
        1935: (JAN, 31),
        1936: (JAN, 20),
        1937: ((JAN, 8), (DEC, 28)),
        1938: (DEC, 17),
        1939: (DEC, 6),
        1940: (NOV, 25),
        1941: (NOV, 14),
        1942: (NOV, 4),
        1943: (OCT, 24),
        1944: (OCT, 12),
        1945: (OCT, 1),
        1946: (SEP, 21),
        1947: (SEP, 11),
        1948: (AUG, 30),
        1949: (AUG, 19),
        1950: (AUG, 9),
        1951: (JUL, 30),
        1952: (JUL, 17),
        1953: (JUL, 7),
        1954: (JUN, 26),
        1955: (JUN, 16),
        1956: (JUN, 4),
        1957: (MAY, 25),
        1958: (MAY, 14),
        1959: (MAY, 4),
        1960: (APR, 21),
        1961: (APR, 11),
        1962: (MAR, 31),
        1963: (MAR, 20),
        1964: (MAR, 9),
        1965: (FEB, 26),
        1966: (FEB, 15),
        1967: (FEB, 5),
        1968: (JAN, 25),
        1969: (JAN, 14),
        1970: ((JAN, 3), (DEC, 24)),
        1971: (DEC, 13),
        1972: (DEC, 1),
        1973: (NOV, 20),
        1974: (NOV, 9),
        1975: (OCT, 30),
        1976: (OCT, 18),
        1977: (OCT, 8),
        1978: (SEP, 27),
        1979: (SEP, 16),
        1980: (SEP, 5),
        1981: (AUG, 25),
        1982: (AUG, 14),
        1983: (AUG, 4),
        1984: (JUL, 24),
        1985: (JUL, 13),
        1986: (JUL, 2),
        1987: (JUN, 21),
        1988: (JUN, 9),
        1989: (MAY, 30),
        1990: (MAY, 20),
        1991: (MAY, 9),
        1992: (APR, 28),
        1993: (APR, 17),
        1994: (APR, 6),
        1995: (MAR, 26),
        1996: (MAR, 14),
        1997: (MAR, 4),
        1998: (FEB, 22),
        1999: (FEB, 11),
        2000: (FEB, 1),
        2001: (JAN, 20),
        2002: ((JAN, 9), (DEC, 29)),
        2003: (DEC, 19),
        2004: (DEC, 8),
        2005: (NOV, 27),
        2006: (NOV, 16),
        2007: (NOV, 6),
        2008: (OCT, 25),
        2009: (OCT, 14),
        2010: (OCT, 4),
        2011: (SEP, 23),
        2012: (SEP, 12),
        2013: (SEP, 1),
        2014: (AUG, 21),
        2015: (AUG, 10),
        2016: (JUL, 30),
        2017: (JUL, 19),
        2018: (JUL, 9),
        2019: (JUN, 28),
        2020: (JUN, 17),
        2021: (JUN, 6),
        2022: (MAY, 26),
        2023: (MAY, 15),
        2024: (MAY, 4),
        2025: (APR, 23),
        2026: (APR, 13),
        2027: (APR, 2),
        2028: (MAR, 21),
        2029: (MAR, 10),
        2030: (FEB, 28),
        2031: (FEB, 17),
        2032: (FEB, 7),
        2033: (JAN, 26),
        2034: (JAN, 16),
        2035: ((JAN, 5), (DEC, 25)),
        2036: (DEC, 13),
        2037: (DEC, 2),
        2038: (NOV, 22),
        2039: (NOV, 12),
        2040: (OCT, 31),
        2041: (OCT, 20),
        2042: (OCT, 9),
        2043: (SEP, 28),
        2044: (SEP, 17),
        2045: (SEP, 7),
        2046: (AUG, 27),
        2047: (AUG, 17),
        2048: (AUG, 5),
        2049: (JUL, 25),
        2050: (JUL, 14),
        2051: (JUL, 4),
        2052: (JUN, 22),
        2053: (JUN, 12),
        2054: (JUN, 2),
        2055: (MAY, 22),
        2056: (MAY, 10),
        2057: (APR, 29),
        2058: (APR, 18),
        2059: (APR, 8),
        2060: (MAR, 28),
        2061: (MAR, 17),
        2062: (MAR, 6),
        2063: (FEB, 23),
        2064: (FEB, 13),
        2065: (FEB, 1),
        2066: (JAN, 21),
        2067: (JAN, 11),
        2068: ((JAN, 1), (DEC, 20)),
        2069: (DEC, 9),
        2070: (NOV, 28),
        2071: (NOV, 17),
        2072: (NOV, 6),
        2073: (OCT, 26),
        2074: (OCT, 16),
        2075: (OCT, 5),
        2076: (SEP, 23),
        2077: (SEP, 12),
    }

    TASUA_DATES = {
        1924: (AUG, 9),
        1925: (JUL, 31),
        1926: (JUL, 19),
        1927: (JUL, 9),
        1928: (JUN, 27),
        1929: (JUN, 16),
        1930: (JUN, 5),
        1931: (MAY, 27),
        1932: (MAY, 15),
        1933: (MAY, 4),
        1934: (APR, 23),
        1935: (APR, 13),
        1936: (APR, 1),
        1937: (MAR, 22),
        1938: (MAR, 10),
        1939: (FEB, 28),
        1940: (FEB, 17),
        1941: (FEB, 5),
        1942: (JAN, 26),
        1943: (JAN, 15),
        1944: ((JAN, 4), (DEC, 24)),
        1945: (DEC, 13),
        1946: (DEC, 3),
        1947: (NOV, 22),
        1948: (NOV, 10),
        1949: (OCT, 31),
        1950: (OCT, 21),
        1951: (OCT, 10),
        1952: (SEP, 29),
        1953: (SEP, 18),
        1954: (SEP, 7),
        1955: (AUG, 28),
        1956: (AUG, 16),
        1957: (AUG, 5),
        1958: (JUL, 26),
        1959: (JUL, 15),
        1960: (JUL, 3),
        1961: (JUN, 22),
        1962: (JUN, 11),
        1963: (JUN, 1),
        1964: (MAY, 20),
        1965: (MAY, 9),
        1966: (APR, 29),
        1967: (APR, 19),
        1968: (APR, 7),
        1969: (MAR, 27),
        1970: (MAR, 17),
        1971: (MAR, 6),
        1972: (FEB, 24),
        1973: (FEB, 12),
        1974: (FEB, 1),
        1975: (JAN, 21),
        1976: ((JAN, 10), (DEC, 30)),
        1977: (DEC, 19),
        1978: (DEC, 9),
        1979: (NOV, 28),
        1980: (NOV, 17),
        1981: (NOV, 5),
        1982: (OCT, 26),
        1983: (OCT, 15),
        1984: (OCT, 4),
        1985: (SEP, 23),
        1986: (SEP, 13),
        1987: (SEP, 2),
        1988: (AUG, 21),
        1989: (AUG, 10),
        1990: (JUL, 31),
        1991: (JUL, 20),
        1992: (JUL, 9),
        1993: (JUN, 29),
        1994: (JUN, 18),
        1995: (JUN, 7),
        1996: (MAY, 26),
        1997: (MAY, 15),
        1998: (MAY, 5),
        1999: (APR, 25),
        2000: (APR, 14),
        2001: (APR, 3),
        2002: (MAR, 23),
        2003: (MAR, 12),
        2004: (FEB, 29),
        2005: (FEB, 18),
        2006: (FEB, 8),
        2007: (JAN, 28),
        2008: (JAN, 18),
        2009: ((JAN, 6), (DEC, 26)),
        2010: (DEC, 15),
        2011: (DEC, 4),
        2012: (NOV, 23),
        2013: (NOV, 12),
        2014: (NOV, 2),
        2015: (OCT, 22),
        2016: (OCT, 10),
        2017: (SEP, 29),
        2018: (SEP, 19),
        2019: (SEP, 8),
        2020: (AUG, 28),
        2021: (AUG, 17),
        2022: (AUG, 7),
        2023: (JUL, 27),
        2024: (JUL, 15),
        2025: (JUL, 4),
        2026: (JUN, 24),
        2027: (JUN, 14),
        2028: (JUN, 2),
        2029: (MAY, 22),
        2030: (MAY, 11),
        2031: (MAY, 1),
        2032: (APR, 19),
        2033: (APR, 9),
        2034: (MAR, 29),
        2035: (MAR, 19),
        2036: (MAR, 7),
        2037: (FEB, 24),
        2038: (FEB, 13),
        2039: (FEB, 3),
        2040: (JAN, 23),
        2041: (JAN, 12),
        2042: ((JAN, 1), (DEC, 22)),
        2043: (DEC, 11),
        2044: (NOV, 29),
        2045: (NOV, 18),
        2046: (NOV, 8),
        2047: (OCT, 28),
        2048: (OCT, 17),
        2049: (OCT, 6),
        2050: (SEP, 25),
        2051: (SEP, 14),
        2052: (SEP, 3),
        2053: (AUG, 23),
        2054: (AUG, 13),
        2055: (AUG, 2),
        2056: (JUL, 22),
        2057: (JUL, 11),
        2058: (JUN, 30),
        2059: (JUN, 19),
        2060: (JUN, 8),
        2061: (MAY, 28),
        2062: (MAY, 18),
        2063: (MAY, 8),
        2064: (APR, 26),
        2065: (APR, 15),
        2066: (APR, 4),
        2067: (MAR, 24),
        2068: (MAR, 13),
        2069: (MAR, 3),
        2070: (FEB, 20),
        2071: (FEB, 9),
        2072: (JAN, 29),
        2073: (JAN, 17),
        2074: ((JAN, 7), (DEC, 27)),
        2075: (DEC, 17),
        2076: (DEC, 5),
    }

    def _get_holiday(self, holiday: str, year: int) -> Iterable[tuple[date, bool]]:
        estimated_dates = getattr(self, f"{holiday}_DATES", {})
        exact_dates = getattr(self, f"{holiday}_DATES_{_CustomCalendar.CUSTOM_ATTR_POSTFIX}", {})
        for year in (year - 1, year):
            for dt in _normalize_tuple(exact_dates.get(year, estimated_dates.get(year, ()))):
                yield date(year, *dt), year not in exact_dates

    def ali_al_rida_death_dates(self, year: int) -> Iterable[tuple[date, bool]]:
        return self._get_holiday(ALI_AL_RIDA_DEATH, year)

    def ali_birthday_dates(self, year: int) -> Iterable[tuple[date, bool]]:
        return self._get_holiday(ALI_BIRTHDAY, year)

    def ali_death_dates(self, year: int) -> Iterable[tuple[date, bool]]:
        return self._get_holiday(ALI_DEATH, year)

    def arbaeen_dates(self, year: int) -> Iterable[tuple[date, bool]]:
        return self._get_holiday(ARBAEEN, year)

    def ashura_dates(self, year: int) -> Iterable[tuple[date, bool]]:
        return self._get_holiday(ASHURA, year)

    def eid_al_adha_dates(self, year: int) -> Iterable[tuple[date, bool]]:
        return self._get_holiday(EID_AL_ADHA, year)

    def eid_al_fitr_dates(self, year: int) -> Iterable[tuple[date, bool]]:
        return self._get_holiday(EID_AL_FITR, year)

    def eid_al_ghadir_dates(self, year: int) -> Iterable[tuple[date, bool]]:
        return self._get_holiday(EID_AL_GHADIR, year)

    def fatima_death_dates(self, year: int) -> Iterable[tuple[date, bool]]:
        return self._get_holiday(FATIMA_DEATH, year)

    def grand_magal_of_touba_dates(self, year: int) -> Iterable[tuple[date, bool]]:
        return self._get_holiday(GRAND_MAGAL_OF_TOUBA, year)

    def hari_hol_johor_dates(self, year: int) -> Iterable[tuple[date, bool]]:
        return self._get_holiday(HARI_HOL_JOHOR, year)

    def hasan_al_askari_death_dates(self, year: int) -> Iterable[tuple[date, bool]]:
        return self._get_holiday(HASAN_AL_ASKARI_DEATH, year)

    def hijri_new_year_dates(self, year: int) -> Iterable[tuple[date, bool]]:
        return self._get_holiday(HIJRI_NEW_YEAR, year)

    def imam_mahdi_birthday_dates(self, year: int) -> Iterable[tuple[date, bool]]:
        return self._get_holiday(IMAM_MAHDI_BIRTHDAY, year)

    def isra_and_miraj_dates(self, year: int) -> Iterable[tuple[date, bool]]:
        return self._get_holiday(ISRA_AND_MIRAJ, year)

    def laylat_al_qadr_dates(self, year: int) -> Iterable[tuple[date, bool]]:
        return self._get_holiday(LAYLAT_AL_QADR, year)

    def maldives_embraced_islam_day_dates(self, year: int) -> Iterable[tuple[date, bool]]:
        return self._get_holiday(MALDIVES_EMBRACED_ISLAM_DAY, year)

    def mawlid_dates(self, year: int) -> Iterable[tuple[date, bool]]:
        return self._get_holiday(MAWLID, year)

    def nuzul_al_quran_dates(self, year: int) -> Iterable[tuple[date, bool]]:
        return self._get_holiday(NUZUL_AL_QURAN, year)

    def prophet_death_dates(self, year: int) -> Iterable[tuple[date, bool]]:
        return self._get_holiday(PROPHET_DEATH, year)

    def quamee_dhuvas_dates(self, year: int) -> Iterable[tuple[date, bool]]:
        return self._get_holiday(QUAMEE_DHUVAS, year)

    def ramadan_beginning_dates(self, year: int) -> Iterable[tuple[date, bool]]:
        return self._get_holiday(RAMADAN_BEGINNING, year)

    def sadiq_birthday_dates(self, year: int) -> Iterable[tuple[date, bool]]:
        return self._get_holiday(SADIQ_BIRTHDAY, year)

    def sadiq_death_dates(self, year: int) -> Iterable[tuple[date, bool]]:
        return self._get_holiday(SADIQ_DEATH, year)

    def tasua_dates(self, year: int) -> Iterable[tuple[date, bool]]:
        return self._get_holiday(TASUA, year)


class _CustomIslamicHolidays(_CustomCalendar, _IslamicLunar):
    pass
