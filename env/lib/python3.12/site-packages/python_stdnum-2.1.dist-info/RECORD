python_stdnum-2.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
python_stdnum-2.1.dist-info/METADATA,sha256=UuhSPZX-ARs5okUopEo6spfd-Bc4jKT0mvF2qp0HhZs,18551
python_stdnum-2.1.dist-info/RECORD,,
python_stdnum-2.1.dist-info/WHEEL,sha256=CmyFI0kx5cdEMTLiONQRbGQwjIoR1aIYB7eCAQ4KPJ0,91
python_stdnum-2.1.dist-info/licenses/COPYING,sha256=XfBwBxmJicYi9dQd6NcD577z0OedYuJDMu5zmkUq9io,26436
python_stdnum-2.1.dist-info/top_level.txt,sha256=2TbxjF-YBqLmFtkp0c2tNeXjolXDpXEHdX05q9nqOKs,7
stdnum/__init__.py,sha256=-ZFzy0-EjvsBLVBenQv_3HfDuM_-GevwK2sYj4FG-hY,1523
stdnum/__pycache__/__init__.cpython-312.pyc,,
stdnum/__pycache__/bic.cpython-312.pyc,,
stdnum/__pycache__/bitcoin.cpython-312.pyc,,
stdnum/__pycache__/casrn.cpython-312.pyc,,
stdnum/__pycache__/cfi.cpython-312.pyc,,
stdnum/__pycache__/cusip.cpython-312.pyc,,
stdnum/__pycache__/damm.cpython-312.pyc,,
stdnum/__pycache__/ean.cpython-312.pyc,,
stdnum/__pycache__/exceptions.cpython-312.pyc,,
stdnum/__pycache__/figi.cpython-312.pyc,,
stdnum/__pycache__/grid.cpython-312.pyc,,
stdnum/__pycache__/gs1_128.cpython-312.pyc,,
stdnum/__pycache__/iban.cpython-312.pyc,,
stdnum/__pycache__/imei.cpython-312.pyc,,
stdnum/__pycache__/imo.cpython-312.pyc,,
stdnum/__pycache__/imsi.cpython-312.pyc,,
stdnum/__pycache__/isan.cpython-312.pyc,,
stdnum/__pycache__/isbn.cpython-312.pyc,,
stdnum/__pycache__/isil.cpython-312.pyc,,
stdnum/__pycache__/isin.cpython-312.pyc,,
stdnum/__pycache__/ismn.cpython-312.pyc,,
stdnum/__pycache__/isni.cpython-312.pyc,,
stdnum/__pycache__/iso11649.cpython-312.pyc,,
stdnum/__pycache__/iso6346.cpython-312.pyc,,
stdnum/__pycache__/iso9362.cpython-312.pyc,,
stdnum/__pycache__/isrc.cpython-312.pyc,,
stdnum/__pycache__/issn.cpython-312.pyc,,
stdnum/__pycache__/lei.cpython-312.pyc,,
stdnum/__pycache__/luhn.cpython-312.pyc,,
stdnum/__pycache__/mac.cpython-312.pyc,,
stdnum/__pycache__/meid.cpython-312.pyc,,
stdnum/__pycache__/numdb.cpython-312.pyc,,
stdnum/__pycache__/util.cpython-312.pyc,,
stdnum/__pycache__/vatin.cpython-312.pyc,,
stdnum/__pycache__/verhoeff.cpython-312.pyc,,
stdnum/ad/__init__.py,sha256=VTgM6XQgddmYotMWz81e5YnMNyc7KDbMKFaoidx8rnc,975
stdnum/ad/__pycache__/__init__.cpython-312.pyc,,
stdnum/ad/__pycache__/nrt.cpython-312.pyc,,
stdnum/ad/nrt.py,sha256=7DKvzfXlCM__4NaU5fCuMDgHMXVv1uhHD94ctjnJ75Q,2986
stdnum/al/__init__.py,sha256=69TASFh-z3hc2DhN5VJhYANtDaOb-gRBEbXlgoVwTbY,981
stdnum/al/__pycache__/__init__.cpython-312.pyc,,
stdnum/al/__pycache__/nipt.cpython-312.pyc,,
stdnum/al/nipt.py,sha256=MgaPgGcp3jb3_QmHA4ZRo6c9Nu7Qyt0Zd_KBeZoGN0I,2941
stdnum/ar/__init__.py,sha256=F5OuCIHk_Ho_5YiK7ykIkXa34ARqQVq4vhiQPsYMPbQ,1033
stdnum/ar/__pycache__/__init__.cpython-312.pyc,,
stdnum/ar/__pycache__/cbu.cpython-312.pyc,,
stdnum/ar/__pycache__/cuit.cpython-312.pyc,,
stdnum/ar/__pycache__/dni.cpython-312.pyc,,
stdnum/ar/cbu.py,sha256=vJuenW6dvN1FjylWxAfn8i4G3R9fpJjQfXiFfMX7kto,2814
stdnum/ar/cuit.py,sha256=gWFlcj0r1uT23T5RQwRJt80F_Xi8x8ywpiDwRZ3Hcm0,3006
stdnum/ar/dni.py,sha256=nXVsIZSXY_PcWaa8UhVRTCVZpq-kVYP6qtnRxPOOghE,2308
stdnum/at/__init__.py,sha256=S4j3CofxVC2tjAD58GBw5TDn3W-qSM3mZ3ihQQkBWuA,1095
stdnum/at/__pycache__/__init__.cpython-312.pyc,,
stdnum/at/__pycache__/businessid.cpython-312.pyc,,
stdnum/at/__pycache__/postleitzahl.cpython-312.pyc,,
stdnum/at/__pycache__/tin.cpython-312.pyc,,
stdnum/at/__pycache__/uid.cpython-312.pyc,,
stdnum/at/__pycache__/vnr.cpython-312.pyc,,
stdnum/at/businessid.py,sha256=iikrni-z8z3YkBmKr6JxnLfwW-Jy3qIXT8B4pr-eqOk,2305
stdnum/at/fa.dat,sha256=9sny5KVmrtZKx5oBQGUiouD5rmjyhNskDbjtr6Jo9SA,2184
stdnum/at/postleitzahl.dat,sha256=HHBeAZ__ri5uxEIXmBx0w8ShQs6ApNRODKLXjKFAgk8,113504
stdnum/at/postleitzahl.py,sha256=jChwZgrPJQBe90wDoWrgOigRMqcL8hLIAqyCjsiLbDI,2557
stdnum/at/tin.py,sha256=gY6PibeuYZvBA8JPPWBlwjsISHfkWi2qsIqO_VpLD74,3878
stdnum/at/uid.py,sha256=jBhUT_egi0ChcvRBf1RsqqLApiRkQ-5ZtsK1JtJyHdI,2460
stdnum/at/vnr.py,sha256=7iNwM5BTF8l12YwHH5HeHWe1KwtUamuxI_2fNFWYe_4,2547
stdnum/au/__init__.py,sha256=RRyx6A8RsmCYBuyWCIzG2JDId_vR0Q69XHXCdaohugA,976
stdnum/au/__pycache__/__init__.cpython-312.pyc,,
stdnum/au/__pycache__/abn.cpython-312.pyc,,
stdnum/au/__pycache__/acn.cpython-312.pyc,,
stdnum/au/__pycache__/tfn.cpython-312.pyc,,
stdnum/au/abn.py,sha256=j-BBGetX8hceMlakjyNhLgoCyRL02Q-iZd491-n_p-g,2782
stdnum/au/acn.py,sha256=UiKiJ1dWEYupJuSSA-Q3egjQOkxXAZ2d_8pNutzhnSk,2808
stdnum/au/tfn.py,sha256=yACuVuoq-1IZZaJVGqqigxP07vEortrn2UYbX5YkLqc,2756
stdnum/be/__init__.py,sha256=FUnq93XXLlYH_QptLfXDr_lRZsW3ba8QI4geVbyU20s,1050
stdnum/be/__pycache__/__init__.cpython-312.pyc,,
stdnum/be/__pycache__/bis.cpython-312.pyc,,
stdnum/be/__pycache__/eid.cpython-312.pyc,,
stdnum/be/__pycache__/iban.cpython-312.pyc,,
stdnum/be/__pycache__/nn.cpython-312.pyc,,
stdnum/be/__pycache__/ssn.cpython-312.pyc,,
stdnum/be/__pycache__/vat.cpython-312.pyc,,
stdnum/be/banks.dat,sha256=vP2iD9AT99JtJoigBCykEKi_eEzLVigfNEeFul6hd3E,9828
stdnum/be/bis.py,sha256=v33rwgUs3WNrGnC1GHUWjuME3-NZSNleVs5u87-AW6w,4296
stdnum/be/eid.py,sha256=3P9fS8V4V9CgcMypbJbJNda5zwUlafgZgx0UxYsUsx4,3457
stdnum/be/iban.py,sha256=SNrJyr60VXMC8uII3hJkhDHgLhG0Smmnn98-N_9wSwI,3139
stdnum/be/nn.py,sha256=moTjEYRzPwZyowOPVKv7MFpW7blh7vpVea3WS-4Vdac,6802
stdnum/be/ssn.py,sha256=A1qqPM-IaJIg_qrNCOIyjqInvYqd027wr38baB2T3Oo,4942
stdnum/be/vat.py,sha256=WyUwQlTGvJKN0qzmQnSYP-3oeCz4C1h81Lj84gE3Vys,2578
stdnum/bg/__init__.py,sha256=bxXttweQIC5Ihb3-ff8-CqDn4R5CqGT3Fv2sE2FoP6A,872
stdnum/bg/__pycache__/__init__.cpython-312.pyc,,
stdnum/bg/__pycache__/egn.cpython-312.pyc,,
stdnum/bg/__pycache__/pnf.cpython-312.pyc,,
stdnum/bg/__pycache__/vat.cpython-312.pyc,,
stdnum/bg/egn.py,sha256=c7dZgkcpLA08WcSif0BqdznzqWmbI9Q-1UcEUPPVF9Y,3425
stdnum/bg/pnf.py,sha256=YVAy8TOMOJHUVZxufXw9OdlkMZxXPsU7b1QGjlPdoYk,2576
stdnum/bg/vat.py,sha256=z6nkuDrl3mQejdZm-JT9WRpjM2QV18z4INZBDfXlyC8,3389
stdnum/bic.py,sha256=wbuQRkb6xJ9DLEiFeEvjwnrezbqT10fjQb1Vi7D-qGI,2660
stdnum/bitcoin.py,sha256=nrcWrYvFKkv1XpMQj0zEdb2M4A10OkIsQUssF9MeLqA,5449
stdnum/br/__init__.py,sha256=DGkTdxeXMKGYAyEEujVXbf_K8Wc3nNYjmwPuCjeaTcQ,957
stdnum/br/__pycache__/__init__.cpython-312.pyc,,
stdnum/br/__pycache__/cnpj.cpython-312.pyc,,
stdnum/br/__pycache__/cpf.cpython-312.pyc,,
stdnum/br/cnpj.py,sha256=mc-46QWKX1EixfF1owX3yPrCOgCP1glrXlWNHODam68,2980
stdnum/br/cpf.py,sha256=4bAp6sfDuG9ZqOAnOladkJFSR5uwp8pGGeKp0P_ZN2I,2914
stdnum/by/__init__.py,sha256=xpp7oC3AqL58qd7kWDQa5aeFfTQ26eUJlyCoqEZ7t4o,976
stdnum/by/__pycache__/__init__.cpython-312.pyc,,
stdnum/by/__pycache__/unp.cpython-312.pyc,,
stdnum/by/unp.py,sha256=cIFvAL-sOeDs5Qy30rxWY7y2bs7oRENwodMRHmP0-z4,4885
stdnum/ca/__init__.py,sha256=CfL-6e4JMRa8D4mV8TW9796kSCcEN6rtAHBrzJx3Hyc,953
stdnum/ca/__pycache__/__init__.cpython-312.pyc,,
stdnum/ca/__pycache__/bc_phn.cpython-312.pyc,,
stdnum/ca/__pycache__/bn.cpython-312.pyc,,
stdnum/ca/__pycache__/sin.cpython-312.pyc,,
stdnum/ca/bc_phn.py,sha256=VTLOH-de4NWbF3L-nLpdUKsRHkBoyZy837Zv746QtdM,3527
stdnum/ca/bn.py,sha256=0nRgEgLvDle9r_G4eySql0bMYsN-VX1Ce-wqxqU97pg,2660
stdnum/ca/sin.py,sha256=mgvMKsusjfRgZ_i0sgrCs_UqOEGr8dwyeEpUlw_jkpM,2718
stdnum/casrn.py,sha256=k-fQQgS3Qcf2LGLpBVRATe3hsSTeC-5roFueGxMDp98,2550
stdnum/cfi.dat,sha256=_gQpvA8iQpdJ2yutWy1pZCrZ0vOgBxJQo3STtunREvY,36681
stdnum/cfi.py,sha256=vxFeZ7h_i-v24fEfbKvMCGw1gLsRG1w1U0BBF7KE7w0,3202
stdnum/ch/__init__.py,sha256=ajzUrF-bUK6t_nzy-pa3bVpiRwsXfzF3Zy5gjBqOwV4,866
stdnum/ch/__pycache__/__init__.cpython-312.pyc,,
stdnum/ch/__pycache__/esr.cpython-312.pyc,,
stdnum/ch/__pycache__/ssn.cpython-312.pyc,,
stdnum/ch/__pycache__/uid.cpython-312.pyc,,
stdnum/ch/__pycache__/vat.cpython-312.pyc,,
stdnum/ch/esr.py,sha256=az5omriQsPWlUsNcCyYyvxMsWAMu5Tv0GEx1-WUD9M8,3474
stdnum/ch/ssn.py,sha256=0VuPWNg4OkQJTgvMTsiQibyJYE-yQ-BAqB-32JYiGac,2715
stdnum/ch/uid.py,sha256=3LtjYKVjWRZghKP-s16kLPFM8Gszadso5zu1Fh2buo0,6185
stdnum/ch/vat.py,sha256=W5_HCJu8MNC_ys9g0FaUC9bAgdFRtn42rciWgoLvbFU,2676
stdnum/cl/__init__.py,sha256=JxtSrEpN0CBCE5-7WeBSIviOA-WBUfYKu277xcXxttk,1058
stdnum/cl/__pycache__/__init__.cpython-312.pyc,,
stdnum/cl/__pycache__/rut.cpython-312.pyc,,
stdnum/cl/rut.py,sha256=FOMd9ceNGqdPXhbMlbNm_Tw5TMp6DM52ced3tNfnWRQ,2948
stdnum/cn/__init__.py,sha256=mhcLBDWQsRHwYYf16MJI9B9V8LU6ZIWhn90jZwIbe0A,989
stdnum/cn/__pycache__/__init__.cpython-312.pyc,,
stdnum/cn/__pycache__/ric.cpython-312.pyc,,
stdnum/cn/__pycache__/uscc.cpython-312.pyc,,
stdnum/cn/loc.dat,sha256=3KcBFEKByRmsL-G9I-ZkYU3q--cy_xVIjWp7VppwcFM,251809
stdnum/cn/ric.py,sha256=aALJufP9cDypw2Mm62_7HaEp5KRcwnus_Xkq0ctINFY,3594
stdnum/cn/uscc.py,sha256=wGW8e4UjrlV8I1XTNeeub4Cq-yMVwPdFRvps20jBoWI,4292
stdnum/co/__init__.py,sha256=Xp_zlA8EAlpUIKTTqPGdCfr4px3TruGa4D5SMnVmAuI,1062
stdnum/co/__pycache__/__init__.cpython-312.pyc,,
stdnum/co/__pycache__/nit.cpython-312.pyc,,
stdnum/co/nit.py,sha256=GNlRiMupdeTuxJ3kEOtrtQnb9Z5MA0Bw78bBJaSWEqA,2722
stdnum/cr/__init__.py,sha256=ZNWzK2St6R5UkOJTQwBp6okBMIeyXgdMDiEAiAh0L_g,962
stdnum/cr/__pycache__/__init__.cpython-312.pyc,,
stdnum/cr/__pycache__/cpf.cpython-312.pyc,,
stdnum/cr/__pycache__/cpj.cpython-312.pyc,,
stdnum/cr/__pycache__/cr.cpython-312.pyc,,
stdnum/cr/cpf.py,sha256=qAKMLuWicFtlCVnTw7zntOksNHHK9hQMpjWBPKZtXcQ,3347
stdnum/cr/cpj.py,sha256=BvmTWI-mWYJ1oOy49g-27Iu9NdgSzgAJ5ejRpk2kfJ0,3657
stdnum/cr/cr.py,sha256=B4KobOcHvHOAAjIkpQVpUjPixmbq0TI6ev4_-wmiFEM,3036
stdnum/cu/__init__.py,sha256=gpdPhdsiJnAW2TFPifT0Rzt-MrLe1_W_MyEUTsxfBho,864
stdnum/cu/__pycache__/__init__.cpython-312.pyc,,
stdnum/cu/__pycache__/ni.cpython-312.pyc,,
stdnum/cu/ni.py,sha256=d_vFjlhtp0PmAhk0lOdWHL1Kjqms0fR7m5b8-3dtbN4,3170
stdnum/cusip.py,sha256=oeHru5Pljn-YftK0ywhqQAT66r6PJ5SDK7AM3NVj2WM,2879
stdnum/cy/__init__.py,sha256=aaNIXKyro60dmmIN8Ml7LHKMVNWNGz6eZ9TFI3TwwxI,868
stdnum/cy/__pycache__/__init__.cpython-312.pyc,,
stdnum/cy/__pycache__/vat.cpython-312.pyc,,
stdnum/cy/vat.py,sha256=zWMhaCc-P-tmBIZVXWyVGXR5QV5Cij1890kGagh5SNc,2751
stdnum/cz/__init__.py,sha256=2TMYANd2eQo81h-G6m-d_RE1v-D9vPnEkgiGuDxjv1w,974
stdnum/cz/__pycache__/__init__.cpython-312.pyc,,
stdnum/cz/__pycache__/bankaccount.cpython-312.pyc,,
stdnum/cz/__pycache__/dic.cpython-312.pyc,,
stdnum/cz/__pycache__/rc.cpython-312.pyc,,
stdnum/cz/bankaccount.py,sha256=Rx2WMKHExnDkZfgznUR_QC2UQ1zprNBaGCjZd-dU4aE,4387
stdnum/cz/banks.dat,sha256=_9emyQBW7LHeVuih4q6RRsMp3D4J4d4mlQMP7CYLhtU,3304
stdnum/cz/dic.py,sha256=020CJ-4Hz_AmnqCfnba4zc_9MvC-UILWccAweBOAuyE,3569
stdnum/cz/rc.py,sha256=pzdzm-yO0o_NxvWZEmzgHexAB_Mqdr4QKLnfS1c7IoI,3692
stdnum/damm.py,sha256=vxTVUjKMRxx_iHiB47mjSICKHrdBLEZJRCXVQ_McjYA,3647
stdnum/de/__init__.py,sha256=Fw-r1ZEYi4KLkv_A3gzRtmUMaAij3Tc2vOr7epV_Uxg,1008
stdnum/de/__pycache__/__init__.cpython-312.pyc,,
stdnum/de/__pycache__/handelsregisternummer.cpython-312.pyc,,
stdnum/de/__pycache__/idnr.cpython-312.pyc,,
stdnum/de/__pycache__/stnr.cpython-312.pyc,,
stdnum/de/__pycache__/vat.cpython-312.pyc,,
stdnum/de/__pycache__/wkn.cpython-312.pyc,,
stdnum/de/handelsregisternummer.py,sha256=buXDTuO5RqrrSK-M5xy5cCQxxdtDvEXenR_xD91QF6o,10757
stdnum/de/idnr.py,sha256=QGSZGBNZFZ05GFsnA-MnZME75Uwwg0p7DxcuaK92qnc,3268
stdnum/de/stnr.py,sha256=IE03kaYfNkoz6caPdtMTYyHkZ_NHtbtzG5g1eGmTc20,7361
stdnum/de/vat.py,sha256=WDK87Mivy84iJBFPqM3vMFJfmxmK2YeLJcBkeoDZIos,2234
stdnum/de/wkn.py,sha256=Jdzm_v-qxQhvMp7i64nF86ZApKcNKRJVEHXSt7O-Dwc,2444
stdnum/dk/__init__.py,sha256=Y2avpoarDiJYKWrBnnvBWs4CLwfceJo9MVfF1Wc7IVE,1022
stdnum/dk/__pycache__/__init__.cpython-312.pyc,,
stdnum/dk/__pycache__/cpr.cpython-312.pyc,,
stdnum/dk/__pycache__/cvr.cpython-312.pyc,,
stdnum/dk/cpr.py,sha256=AhMdIbC6ukn66HRiTlct-n5uSAS-6XPUo7ccQHvN0O4,4303
stdnum/dk/cvr.py,sha256=u0OS9szmBmgdpGlAbO01La_087Th_92gyJ3YrllbiVc,2361
stdnum/do/__init__.py,sha256=Ho6xGrw_DE0obWj6egYNJuXExOdf3fG_1XlujQObRvQ,974
stdnum/do/__pycache__/__init__.cpython-312.pyc,,
stdnum/do/__pycache__/cedula.cpython-312.pyc,,
stdnum/do/__pycache__/ncf.cpython-312.pyc,,
stdnum/do/__pycache__/rnc.cpython-312.pyc,,
stdnum/do/cedula.py,sha256=6gWNqkZQ9StX0apyu4_tAE9FKMSTcIZYItVncCm2tg4,10730
stdnum/do/ncf.py,sha256=rNOjBzMBALNLT1PJIz1ANlO6_DQEsY6yPGyJCvEZMAQ,9226
stdnum/do/rnc.py,sha256=RaVn0uGnAAV47FTrLsZCPKS2eiLzKVc2aQzNDUlHiQU,7787
stdnum/dz/__init__.py,sha256=zGeR4VRIxVyCRUXAbOZQSKSpbJ5FezKhvBDQxilN5uA,982
stdnum/dz/__pycache__/__init__.cpython-312.pyc,,
stdnum/dz/__pycache__/nif.cpython-312.pyc,,
stdnum/dz/nif.py,sha256=2Pub3UD5DP-FG1GxJZtUC1GAnynGGT1I-zL_CzfobzQ,3118
stdnum/ean.py,sha256=ojS_f-jnvlxMs4rb7-DsVvtJnGcUw_aWg084by8HK4M,2600
stdnum/ec/__init__.py,sha256=q_00825eFgpoA5Y_35yYwoGa7kpofd7Ni7A5vVHO-tk,985
stdnum/ec/__pycache__/__init__.cpython-312.pyc,,
stdnum/ec/__pycache__/ci.cpython-312.pyc,,
stdnum/ec/__pycache__/ruc.cpython-312.pyc,,
stdnum/ec/ci.py,sha256=p0EY9LffgIjANFs6UGJVDmEqBomNECOveDBr7LH-nus,2726
stdnum/ec/ruc.py,sha256=tLs3F6OITxAoDvIJOUaZHyzabq9qqHCHTYzuQldpu8Y,3963
stdnum/ee/__init__.py,sha256=qnD04Wkx9c0KOLrurKJSQor0kvkwK9i7Pa2LnDtTnbQ,981
stdnum/ee/__pycache__/__init__.cpython-312.pyc,,
stdnum/ee/__pycache__/ik.cpython-312.pyc,,
stdnum/ee/__pycache__/kmkr.cpython-312.pyc,,
stdnum/ee/__pycache__/registrikood.cpython-312.pyc,,
stdnum/ee/ik.py,sha256=FHLvpdj3vJq5UJeF0Jctb8hYgIho3suhTp-TXieJPuM,3635
stdnum/ee/kmkr.py,sha256=S-JiOB20t9CgU78uwoU15BmJ-VyQjUin7agpfYHdIJw,2321
stdnum/ee/registrikood.py,sha256=0nVxRENiOAYVD_Yj0EADp2__AmAsK2_nVzsYVoLVbZ8,2738
stdnum/eg/__init__.py,sha256=YK1z09zFo2qtjMxPCFWxWu_AttsKbiVotWXI-8jYKco,967
stdnum/eg/__pycache__/__init__.cpython-312.pyc,,
stdnum/eg/__pycache__/tn.cpython-312.pyc,,
stdnum/eg/tn.py,sha256=S4cpuVIOLqcLyeeyVFbYVwKhooytnUt6wiealsLlt8U,3058
stdnum/es/__init__.py,sha256=Wcw83ulg21_IZYbrxcCKzQRcwBnByo1x4JXJC-s8Yn0,978
stdnum/es/__pycache__/__init__.cpython-312.pyc,,
stdnum/es/__pycache__/cae.cpython-312.pyc,,
stdnum/es/__pycache__/ccc.cpython-312.pyc,,
stdnum/es/__pycache__/cif.cpython-312.pyc,,
stdnum/es/__pycache__/cups.cpython-312.pyc,,
stdnum/es/__pycache__/dni.cpython-312.pyc,,
stdnum/es/__pycache__/iban.cpython-312.pyc,,
stdnum/es/__pycache__/nie.cpython-312.pyc,,
stdnum/es/__pycache__/nif.cpython-312.pyc,,
stdnum/es/__pycache__/postal_code.cpython-312.pyc,,
stdnum/es/__pycache__/referenciacatastral.cpython-312.pyc,,
stdnum/es/cae.py,sha256=3OQBJhubKC5xO6bb9jw959Wj894s4b72wb5rGFyrFlk,10000
stdnum/es/ccc.py,sha256=kH7Bw56RS4M4DU5GpxAiv-7IObADS_52lxxBHeCPSoo,4201
stdnum/es/cif.py,sha256=l42n_Wz22sTHfd-BKLxo6jR3-b6-h-IKZ_mSfp7jmXw,3676
stdnum/es/cups.py,sha256=nl8qpL6ViPkwP_z2MaMIhhsZla9FMgdW_w4ZDSHBgn0,3769
stdnum/es/dni.py,sha256=0fyadXpUZ9rXWcrLTfrbep7k02rySwBeywtAw_C_nZc,2618
stdnum/es/iban.py,sha256=aVsf6NrW1Tm2QzD4f9oN-h-Ar_qyqTpXGhyRur3jYPQ,2567
stdnum/es/nie.py,sha256=VIajCt_W0nWzFvsaEQxbCpXde8nsA_ouHQSsDhJ5AXk,2636
stdnum/es/nif.py,sha256=Vb6d138uVgIYmA3F0DrEChtJlVTWlbN7vP6mF1OKv5w,3247
stdnum/es/postal_code.py,sha256=7GR9IzQEe1rtqvdSYb7peJkMFU0HqdzXEqGy-eprsKs,2441
stdnum/es/referenciacatastral.py,sha256=bZtoMGwGPSYfhh9lqTbbT8u3wEIu6CDZn3MYf3psNIg,4175
stdnum/eu/__init__.py,sha256=Eyl7TknedZQYKk4gpxdMpZU8zG2Oz3O0WKBhB6K7I9s,882
stdnum/eu/__pycache__/__init__.cpython-312.pyc,,
stdnum/eu/__pycache__/at_02.cpython-312.pyc,,
stdnum/eu/__pycache__/banknote.cpython-312.pyc,,
stdnum/eu/__pycache__/ecnumber.cpython-312.pyc,,
stdnum/eu/__pycache__/eic.cpython-312.pyc,,
stdnum/eu/__pycache__/nace.cpython-312.pyc,,
stdnum/eu/__pycache__/oss.cpython-312.pyc,,
stdnum/eu/__pycache__/vat.cpython-312.pyc,,
stdnum/eu/at_02.py,sha256=WaGPc7s4HnCNKNteeJQTzOsFQcDJrD1EQQBy3JrSvFU,3251
stdnum/eu/banknote.py,sha256=haVA1s-s9VBpYL86IRWEyXFw1i4iqC-zf85qlRD2LfE,2383
stdnum/eu/ecnumber.py,sha256=SzhkGZCQvBmfjUyfUGU6r9peZR5UX5CxCxMRRQG_oUs,2584
stdnum/eu/eic.py,sha256=nP0bsoOBuu2GWLFqx2eJh5Kf0ENzI4kN29I6ppZ6_Bg,2924
stdnum/eu/nace.dat,sha256=3sJwInkC6T3sKxQspzTmAH0V3wyq2EVNt5nwXoSg2i0,67035
stdnum/eu/nace.py,sha256=It-BwGKlmlZNv3-XBg9NqpnNJat48lx9HXGFvROub10,3966
stdnum/eu/oss.py,sha256=oilc_OoLGJoPiZlBI4GvnQXPvwaqd_aYS1SgG3nYKkE,3947
stdnum/eu/vat.py,sha256=yWErI98fB4ODg2_PJL6RSGjsps_kCPmQJ5H0rbRlQF8,6710
stdnum/exceptions.py,sha256=cVlNlYvhNJKS3NX21VpM2TwBbluK7YcryaRyNMf8P2Q,2468
stdnum/fi/__init__.py,sha256=YHC-vVvce7HzVbGdp0TUOv46_K5rn3R_b6l_a1IHvoc,1091
stdnum/fi/__pycache__/__init__.cpython-312.pyc,,
stdnum/fi/__pycache__/alv.cpython-312.pyc,,
stdnum/fi/__pycache__/associationid.cpython-312.pyc,,
stdnum/fi/__pycache__/hetu.cpython-312.pyc,,
stdnum/fi/__pycache__/veronumero.cpython-312.pyc,,
stdnum/fi/__pycache__/ytunnus.cpython-312.pyc,,
stdnum/fi/alv.py,sha256=keUlE33OrRMr8UqRaGbsp_yZcV2kMQtZ6hru7Xi8o7Q,2262
stdnum/fi/associationid.py,sha256=koUcEGKfOE3evTtKCG31KlQrN7x1J5Y9Cqn1fw4bt5w,2893
stdnum/fi/hetu.py,sha256=xFe8fyDQe9rx6l6yrTk_x8SgbjdZamQ8t6fsBc1GSoI,4089
stdnum/fi/veronumero.py,sha256=pOBpRWbC1kPL4dkxzMxw_SH8Ii129MZ9bis6gfRfL08,2445
stdnum/fi/ytunnus.py,sha256=9REWw4idhLiSF5b67U_qyO72U8FecdPcjNtEnv8kQyM,2061
stdnum/figi.py,sha256=ZE063_aqEq942YUCdTDruZnM8wZSLyxWPXs81mZ6sAs,2976
stdnum/fo/__init__.py,sha256=1V3ss7CDM5kydUsheyx72oaiNcxQECpTSWfRllWnfco,983
stdnum/fo/__pycache__/__init__.cpython-312.pyc,,
stdnum/fo/__pycache__/vn.cpython-312.pyc,,
stdnum/fo/vn.py,sha256=uA6GpfOODMd2D-qLr1CWZrkAy1_Ius7RirdxRO0CCk8,2569
stdnum/fr/__init__.py,sha256=LQm0NcpS6umBQdfjFb3plWZMS8o_UWhuWzI_0cpXsiY,976
stdnum/fr/__pycache__/__init__.cpython-312.pyc,,
stdnum/fr/__pycache__/nif.cpython-312.pyc,,
stdnum/fr/__pycache__/nir.cpython-312.pyc,,
stdnum/fr/__pycache__/siren.cpython-312.pyc,,
stdnum/fr/__pycache__/siret.cpython-312.pyc,,
stdnum/fr/__pycache__/tva.cpython-312.pyc,,
stdnum/fr/nif.py,sha256=6OdqYKQByempjTBQR7emFrSWdlFqbl6DTf0eAMJqScM,3067
stdnum/fr/nir.py,sha256=fBRcqwaSroIr2iKlZyZ2oFJ-SYV7G9KuhfjpKZhax-E,4013
stdnum/fr/siren.py,sha256=bYADteU0x7U4iuE4CMolC-13jxG_i5QxLRnasXd1Kjw,2750
stdnum/fr/siret.py,sha256=JFBOPwLZkGAg19U6eBQ1iy4DyMZaKLSEGqZTB-7_9Fo,3682
stdnum/fr/tva.py,sha256=c9jK-gq-_XfIWq4G8lLgn8nY047e9Iq97Gfe4szqBwM,3511
stdnum/gb/__init__.py,sha256=ZJ6CGiJ8FWTvgjeO-ZySQfdQvJ6IDI2x-NIeR8ZbjB0,882
stdnum/gb/__pycache__/__init__.cpython-312.pyc,,
stdnum/gb/__pycache__/nhs.cpython-312.pyc,,
stdnum/gb/__pycache__/sedol.cpython-312.pyc,,
stdnum/gb/__pycache__/upn.cpython-312.pyc,,
stdnum/gb/__pycache__/utr.cpython-312.pyc,,
stdnum/gb/__pycache__/vat.cpython-312.pyc,,
stdnum/gb/nhs.py,sha256=l6Nxrltn9ICbPFuYwUc6sC3KR9tzOE_6EQrvM6FeeCA,2948
stdnum/gb/sedol.py,sha256=fDPm1ZnuG6PDaR-kRO-qcZRLjApDQk97oYV4c0Sqzws,2848
stdnum/gb/upn.py,sha256=axB-J1gQW468xOu15qsVzEKnbJzg0DtIKhdUE4Gviys,4060
stdnum/gb/utr.py,sha256=Pha7PBLIDwxeJ9U3Ksee-VmG0w0-XN0MtUNokNsp2Bk,2460
stdnum/gb/vat.py,sha256=LBUd9Zuj_pR6QNpfYvjxYnwPLsPK-EG5V1ETjPJIIPA,4379
stdnum/gh/__init__.py,sha256=zNVFKk_fc96QgFHOrr-ZbVqnb_7mLyfBGf6U3J2VzOo,968
stdnum/gh/__pycache__/__init__.cpython-312.pyc,,
stdnum/gh/__pycache__/tin.cpython-312.pyc,,
stdnum/gh/tin.py,sha256=tgP4pSZRv863le6zAt-UElPyWLahEsUAZHAW1aL4DuA,3009
stdnum/gn/__init__.py,sha256=BCf_2hsp2RmYP8EWlA8zYRDhrAQ-gRaAAo0Yt40FCek,971
stdnum/gn/__pycache__/__init__.cpython-312.pyc,,
stdnum/gn/__pycache__/nifp.cpython-312.pyc,,
stdnum/gn/nifp.py,sha256=SSFiSDRxCc9dwCUNRjrL5EmIjPgd67ws3vA1OpJ_1gs,2621
stdnum/gr/__init__.py,sha256=N-2f6pGt7Q0weSyb6hzGWwgqUUjYZfPxnCO5sDJneu4,864
stdnum/gr/__pycache__/__init__.cpython-312.pyc,,
stdnum/gr/__pycache__/amka.cpython-312.pyc,,
stdnum/gr/__pycache__/vat.cpython-312.pyc,,
stdnum/gr/amka.py,sha256=HHsxTFEOkin0nk1UHlZuuv85n1qNHOJFCjWGxmiWr-0,3187
stdnum/gr/vat.py,sha256=jqKj7VlmfoS36ErhQtCBWUOMaX7u00zMMYjdjMpNDSI,2545
stdnum/grid.py,sha256=ASN-XKMIpXA4qClMG39BgrC542fDxuR2ooz8OO3rCAc,2542
stdnum/gs1_128.py,sha256=ff87bVtUFlin7bneJsberspF97c_1-tPEwF_1sRfwLg,11414
stdnum/gs1_ai.dat,sha256=MGEfM_AYlOqC30vJFVBqE81oTGr7zkif1qkClFkVBgQ,24313
stdnum/gt/__init__.py,sha256=8PtgkcSPPM4bhJsXX2x1GaZovx3URd0nGHj-7isrbuo,978
stdnum/gt/__pycache__/__init__.cpython-312.pyc,,
stdnum/gt/__pycache__/nit.cpython-312.pyc,,
stdnum/gt/nit.py,sha256=k8uxu03wnTyJPuvro6hy730qrsndkNo90NNX6sZmtP4,3231
stdnum/hr/__init__.py,sha256=-9kYBji6nUMgEuvmvTySv4Q5-olr9qYRQeUjqbpPxFM,980
stdnum/hr/__pycache__/__init__.cpython-312.pyc,,
stdnum/hr/__pycache__/oib.cpython-312.pyc,,
stdnum/hr/oib.py,sha256=vVOgyA9Ioaxm7IkuIjROx7uGBqf3RLqSjMpe1hFESgE,2287
stdnum/hu/__init__.py,sha256=yn4z0jT5jK7xyBYSLPeeE8uaWbj6aG4rXDM0DBniFYE,983
stdnum/hu/__pycache__/__init__.cpython-312.pyc,,
stdnum/hu/__pycache__/anum.cpython-312.pyc,,
stdnum/hu/anum.py,sha256=MfFtFPjGlvNGns4jPpYP-8G102-F0mOEhAEcdQ0F1pk,2405
stdnum/iban.dat,sha256=4fqKRPWB1z3EPs-xv9Hm3UAwwkNqrV42fw83fRhXemw,3663
stdnum/iban.py,sha256=eIGRQbR5EEdY74M-88TexaFGvzKpcdRYokMsfu9TMdc,4755
stdnum/id/__init__.py,sha256=IX1Ye5LPmW08edLIQoa5n2w6Dbu2Nw3pkkKpt-tJpA8,979
stdnum/id/__pycache__/__init__.cpython-312.pyc,,
stdnum/id/__pycache__/nik.cpython-312.pyc,,
stdnum/id/__pycache__/npwp.cpython-312.pyc,,
stdnum/id/loc.dat,sha256=WYFbHp5r3hX2R_brUmDB-Be636coMsgAMZWTa65ua3M,15595
stdnum/id/nik.py,sha256=lyRXYTGlERRDSfHIhPOAg60MnECJ6bHE8l4D7iTLcmU,3759
stdnum/id/npwp.py,sha256=JbmBmPov32lJV22-GZmYRWYyTp1Yw_PYtwseiWG5J5w,3494
stdnum/ie/__init__.py,sha256=qRZkaTMOl9PxzcpdegS7QA9osO_J1Hs-NTMz1VTcEPs,864
stdnum/ie/__pycache__/__init__.cpython-312.pyc,,
stdnum/ie/__pycache__/pps.cpython-312.pyc,,
stdnum/ie/__pycache__/vat.cpython-312.pyc,,
stdnum/ie/pps.py,sha256=spQ2D9Lt5FTRk3WBRVUz9WIkZt166ecMMlQvOf2ZyvI,3320
stdnum/ie/vat.py,sha256=CZEAto-ki1Pa29GmkmhcCK5WHrS-wV2V0cjX8k82OIQ,3808
stdnum/il/__init__.py,sha256=WoGwtthxB3wDKu3I3EWN6hKtbfn_lYu9lS-VExnDIOQ,969
stdnum/il/__pycache__/__init__.cpython-312.pyc,,
stdnum/il/__pycache__/hp.cpython-312.pyc,,
stdnum/il/__pycache__/idnr.cpython-312.pyc,,
stdnum/il/hp.py,sha256=Xstdsi3a7YdxNLwFZdmaLaokasQk8lmjMPrS8az3yxs,2885
stdnum/il/idnr.py,sha256=5kFwcgXTbpT1FEXoLQ4DXg0IHGUblYkNuyeqX6T__ow,2730
stdnum/imei.py,sha256=p_liR56JYm3mP2awVW3gDRhJY5uFTRcSxBddOTbnPdU,3734
stdnum/imo.py,sha256=DSmNsEEh7U_N1h7AVBlnC-MfaUIASGNXjPeRszk31rs,2821
stdnum/imsi.dat,sha256=QB1B5cgxZFNQv3bQJVIhgQiXLIamRR9-FDmX7iAIzas,377593
stdnum/imsi.py,sha256=LFjt2EPLt-cVU4pwzAu7ISFxmY-4FVb3Wq8bYT8jPZ4,3128
stdnum/in_/__init__.py,sha256=i-FZxVG2Vry-ycOzeGj7xevY2OZO-NmfUqgaV897GCM,976
stdnum/in_/__pycache__/__init__.cpython-312.pyc,,
stdnum/in_/__pycache__/aadhaar.cpython-312.pyc,,
stdnum/in_/__pycache__/epic.cpython-312.pyc,,
stdnum/in_/__pycache__/gstin.cpython-312.pyc,,
stdnum/in_/__pycache__/pan.cpython-312.pyc,,
stdnum/in_/__pycache__/vid.cpython-312.pyc,,
stdnum/in_/aadhaar.py,sha256=tXndCxnY0sWi4-x00YzP61jFDmATTTkno35b9kST1zM,3689
stdnum/in_/epic.py,sha256=R-ayQFULt7CpCHnFdQ1dv1aKfReSHx2VBkIxAnEmaZw,2895
stdnum/in_/gstin.py,sha256=RA6leL9RRKLjOsdPAeZAJA_3SWNgTeTpXg4AKEQVixU,4941
stdnum/in_/pan.py,sha256=vSzHJNa8aG1H7psluivNeC_0VfkMvR2OOXPmXfFxZVk,4361
stdnum/in_/vid.py,sha256=CYesQCssqSxrm9Jy4ur724Ng08AnSe8xbxyVth67FXE,3672
stdnum/is_/__init__.py,sha256=rrrsM3Skge3f1TILDFRzQp-I5PyZatKg1OqDh1VFWTM,1037
stdnum/is_/__pycache__/__init__.cpython-312.pyc,,
stdnum/is_/__pycache__/kennitala.cpython-312.pyc,,
stdnum/is_/__pycache__/vsk.cpython-312.pyc,,
stdnum/is_/kennitala.py,sha256=XUUmO9BSxqc_IPAC5vZRo-ixG-Vsk0jzhWafScHPv0g,3756
stdnum/is_/vsk.py,sha256=EuzTiZPi3OiYRFjlbLMvJlQhLzJ6UEuVrxoZmMu2pJc,2047
stdnum/isan.py,sha256=Zwye5JjVfsH0PjwJB3g5ai29WVzwn8QkK3sRsxOHt-Y,6307
stdnum/isbn.dat,sha256=vo03a5YklyAoVNxoCfeTE4vK8oZyWLdGph0oE7wpv_k,21861
stdnum/isbn.py,sha256=3Z9YJctBceXS6AYhDU3fATnXLDZopVGI8RY468IGl2Q,7444
stdnum/isil.dat,sha256=OgFz_uZGjn3Yl2JzWmzj4ufbSfECa6Hi06HeGjYpPaY,4678
stdnum/isil.py,sha256=lfA3m2_YnfTGsZ4K57miI-auhiBo4VvB0dYAQKeegpQ,3760
stdnum/isin.py,sha256=ElbeyIXRQx_eIt7K1yRbCxM31ltNRYWrBrqFt1zLImc,5581
stdnum/ismn.py,sha256=kdQNQY5VWmRknqu9_UOvyat_HtuL34mM-IBwqpXMkd0,4712
stdnum/isni.py,sha256=qZNc3v7Q50Neeieb2MgKj0wdHNd1ssqPm_wmfi5pmdw,2669
stdnum/iso11649.py,sha256=WBHkUjU-xmtcKjsD2p3Ru3pU6cI_Q7l-69jfFe420Oo,3018
stdnum/iso6346.py,sha256=T14uEhLpTsJ9vj7EbLbqZ2ja3JxudeKmGrxsMjhOrVI,3235
stdnum/iso7064/__init__.py,sha256=SEyBlYRMp0ONFnKtwZunDJfCMh7mBWMfulnmQDc45AE,1363
stdnum/iso7064/__pycache__/__init__.cpython-312.pyc,,
stdnum/iso7064/__pycache__/mod_11_10.cpython-312.pyc,,
stdnum/iso7064/__pycache__/mod_11_2.cpython-312.pyc,,
stdnum/iso7064/__pycache__/mod_37_2.cpython-312.pyc,,
stdnum/iso7064/__pycache__/mod_37_36.cpython-312.pyc,,
stdnum/iso7064/__pycache__/mod_97_10.cpython-312.pyc,,
stdnum/iso7064/mod_11_10.py,sha256=m4emwbGiYG506kre14JPIwbmRdJnvBikYnN7UxCRusc,2201
stdnum/iso7064/mod_11_2.py,sha256=4jpGh6KEPQsXnXt3zHk67zDGTjLppqTpt2r28Q0BEaY,2229
stdnum/iso7064/mod_37_2.py,sha256=JKfOUPJ6RSJKpTnicLFFhJnTXD9MwbXOKyCcOyPGEP8,2649
stdnum/iso7064/mod_37_36.py,sha256=SDHI28KxBKFnhjGwI0mln-kcKeXkXQy0NkmMkp4SkBY,2703
stdnum/iso7064/mod_97_10.py,sha256=_9cmQqnhqArfwNE7oQqCcVJWe-DxZrEXzvfaKGpY7Tw,2356
stdnum/iso9362.py,sha256=J2KFDanGK3KiNmdcCwB928ZWCgJcy2LSNPorqIgHMfM,1124
stdnum/isrc.py,sha256=rPGlCmPNk_UF3_1lA2-eOgdXPsG1HMbT1zjREgyDq-A,3788
stdnum/issn.py,sha256=3SwRAd2rT3WNUZrwram3J_8z7YepfnMhk6NeJAMicko,3239
stdnum/it/__init__.py,sha256=dQwojR9mKiMZUSmloYcvL1uRt0Hj3Mx_AOoMbLDg-Ds,978
stdnum/it/__pycache__/__init__.cpython-312.pyc,,
stdnum/it/__pycache__/aic.cpython-312.pyc,,
stdnum/it/__pycache__/codicefiscale.cpython-312.pyc,,
stdnum/it/__pycache__/iva.cpython-312.pyc,,
stdnum/it/aic.py,sha256=0peBw4_zFx9T8eLTU0O-rMXSCwIRGcEE0aPEIa4wGZQ,4351
stdnum/it/codicefiscale.py,sha256=XfhDpJjXw-hRk40ZeYRLYIYIlXaImk79n_obFIwyBug,5948
stdnum/it/iva.py,sha256=GH0i-Hpwt_SC6OHGFGtV7EX355Mx_MnjJSrk2UsaINc,2502
stdnum/jp/__init__.py,sha256=Q0e5fhbmzYznUK7J450AkcGMzEd_Lja-oK8ruemL678,953
stdnum/jp/__pycache__/__init__.cpython-312.pyc,,
stdnum/jp/__pycache__/cn.cpython-312.pyc,,
stdnum/jp/__pycache__/in_.cpython-312.pyc,,
stdnum/jp/cn.py,sha256=zwDyxh4pwC7f8ZPlFya0muiSIULveGlRw-pc2U6rXSY,2869
stdnum/jp/in_.py,sha256=bBZY0ywIOACl5zCfnGY4uTWVMNTBG7TE9c16ju8TO84,3033
stdnum/ke/__init__.py,sha256=OmnBYjmVTdLNqEcVW4L-F95XP6WzkqfNUl635PONS6A,970
stdnum/ke/__pycache__/__init__.cpython-312.pyc,,
stdnum/ke/__pycache__/pin.cpython-312.pyc,,
stdnum/ke/pin.py,sha256=xsmCkFLbv0G4Mr7faBmkCPrvEMAEMVEbJW8-p1UyYmk,3058
stdnum/kr/__init__.py,sha256=5S_krDeez4Jv6kKSdrogOf39lkpNeZD03iOiY9QJxq8,986
stdnum/kr/__pycache__/__init__.cpython-312.pyc,,
stdnum/kr/__pycache__/brn.cpython-312.pyc,,
stdnum/kr/__pycache__/rrn.cpython-312.pyc,,
stdnum/kr/brn.py,sha256=pBRqje2N-HPcOVCBheeQcSPzwA-47Ba2WO_ZO0chHcg,2793
stdnum/kr/rrn.py,sha256=SswWrk9B4enMrMNxon30WfbcjT6H3ZKp_wWiLfh6-E0,4627
stdnum/lei.py,sha256=MB_IV1Q_YtUlA-gxtWEmVWiargJ7zYvUWiOowf76u-8,2253
stdnum/li/__init__.py,sha256=RAjw4xK4qKH7HjUHhSFAvMgADsEtU1uFKmr186gfxtM,881
stdnum/li/__pycache__/__init__.cpython-312.pyc,,
stdnum/li/__pycache__/peid.cpython-312.pyc,,
stdnum/li/peid.py,sha256=FcRl37qW-TqdYdr82JrNHa1cmEnWihEwkjRQnQrX3x0,2308
stdnum/lt/__init__.py,sha256=E2h6G4J5eHBHgRz1vdMOQXMvn6YjLMpQUO21_CjhvpI,984
stdnum/lt/__pycache__/__init__.cpython-312.pyc,,
stdnum/lt/__pycache__/asmens.cpython-312.pyc,,
stdnum/lt/__pycache__/pvm.cpython-312.pyc,,
stdnum/lt/asmens.py,sha256=ecCMN8YXeEr7Jh5gThkQTI1YbQtWEdQZyc_ylQm3yCI,2580
stdnum/lt/pvm.py,sha256=4N7KSAwU5DzFY4OEMxznYzijVebrwcISnxRSOfq9IgM,3075
stdnum/lu/__init__.py,sha256=TwP_kopH4JwoAQ08ikdQ7vf2kFwWa1PaF_TnWy53KF8,990
stdnum/lu/__pycache__/__init__.cpython-312.pyc,,
stdnum/lu/__pycache__/tva.cpython-312.pyc,,
stdnum/lu/tva.py,sha256=H4YF9H4CfWClHkpYAURUAMzKqal8aZnmL44TtllTKSY,2396
stdnum/luhn.py,sha256=rIftrj53DRGlCMmhW7TICrs0dyzDBEw0qkyoXmR1lNg,2807
stdnum/lv/__init__.py,sha256=x0oTp8fcNu3483JcRlUwDF7L_S-HNF6luk38SNqOeHU,978
stdnum/lv/__pycache__/__init__.cpython-312.pyc,,
stdnum/lv/__pycache__/pvn.cpython-312.pyc,,
stdnum/lv/pvn.py,sha256=mqrdg8xeHSIxTewogSraJyLFnfDh9uuFYADgcBW6L80,3995
stdnum/ma/__init__.py,sha256=3c-nIyTEktE_Z3CRLP6x9kIxE4zmIX7-QODntqai9m0,982
stdnum/ma/__pycache__/__init__.cpython-312.pyc,,
stdnum/ma/__pycache__/ice.cpython-312.pyc,,
stdnum/ma/ice.py,sha256=coxkIhMALGo08gX8UYsXaODqt_fma-EQMrVeZ1CqFkU,3330
stdnum/mac.py,sha256=4WkLq-c6lQNbFWSVgnZRSxyEYoz2k4IAuxMhuf9sIAE,5037
stdnum/mc/__init__.py,sha256=qlmMk4dJUmsqYLnyBy0lr6QuLPEyO_dqsa-fc8f2mR0,978
stdnum/mc/__pycache__/__init__.cpython-312.pyc,,
stdnum/mc/__pycache__/tva.cpython-312.pyc,,
stdnum/mc/tva.py,sha256=We--rYIoxGXCgp6xgkCWeKVBkQuSSPoxsKMRzdGoWnw,2057
stdnum/md/__init__.py,sha256=ic7n17YHMlJ6IHLZwZN80oEmEDiNWOWn7TG7t8bVykc,872
stdnum/md/__pycache__/__init__.cpython-312.pyc,,
stdnum/md/__pycache__/idno.cpython-312.pyc,,
stdnum/md/idno.py,sha256=b3Gd84-D0FStY4Fo6A1YDSFzt7d-95kRf5UjSLJo9HM,2520
stdnum/me/__init__.py,sha256=afjhC9nu4Mo8a4-QiGSqGafaYjZ8Wf2enbznTnuS4Ik,976
stdnum/me/__pycache__/__init__.cpython-312.pyc,,
stdnum/me/__pycache__/iban.cpython-312.pyc,,
stdnum/me/__pycache__/pib.cpython-312.pyc,,
stdnum/me/iban.py,sha256=Glbb1-e3FMCcnn7_lkHEMwhPd0FRDGU3J4OGBbjiW30,2322
stdnum/me/pib.py,sha256=au0gfiiXh93_wiQ8WghEtfUxICQp6K3o65PhFO-Gbzc,2454
stdnum/meid.py,sha256=7ceinjolRBHd6lt13MtsXX7fCduoB2ikHXPiTPv_Sgw,6894
stdnum/mk/__init__.py,sha256=p34Ggw2t5YTJ0Jby05OaW6Xrr2gCkI7POPir-38aOkE,988
stdnum/mk/__pycache__/__init__.cpython-312.pyc,,
stdnum/mk/__pycache__/edb.cpython-312.pyc,,
stdnum/mk/edb.py,sha256=UASgo-FZOyKbyF4Pwa4mIhLlq3x-LooqYr6Jda47gGU,3012
stdnum/mt/__init__.py,sha256=rLVwjU64pROIUOfHvNWvV4sLSrfLuYXmrQB-HhZJ-ME,868
stdnum/mt/__pycache__/__init__.cpython-312.pyc,,
stdnum/mt/__pycache__/vat.cpython-312.pyc,,
stdnum/mt/vat.py,sha256=Ttyf75ctjh2GI7orQkeBFrlRehEFjManZ2lz3V2ZFXQ,2274
stdnum/mu/__init__.py,sha256=6ZHvCTeDRiJTarS8AYiyKaj__IGiIPd48OllNLlsQ3M,872
stdnum/mu/__pycache__/__init__.cpython-312.pyc,,
stdnum/mu/__pycache__/nid.cpython-312.pyc,,
stdnum/mu/nid.py,sha256=3ZCeyTuq6cwcKalpbuQL77KuDVxI4Q8qK6HtmbWT0Bs,3017
stdnum/mx/__init__.py,sha256=1BZE4SRmG239iLoPrlNKZUm53h8XMFt6wEvVlxrePlA,1030
stdnum/mx/__pycache__/__init__.cpython-312.pyc,,
stdnum/mx/__pycache__/curp.cpython-312.pyc,,
stdnum/mx/__pycache__/rfc.cpython-312.pyc,,
stdnum/mx/curp.py,sha256=YW43vffHUWcYn75fPKPgyNw9fIQ164N1FTz9WRSOyQM,4444
stdnum/mx/rfc.py,sha256=O-JKK064qYu9BLOY35-uOomdwJwTvcdJJEuyCOL5SG0,5463
stdnum/my/__init__.py,sha256=8I7UPXtTDgbydzOtTnfFOkeznBZetfjT4T1reaV7XIk,872
stdnum/my/__pycache__/__init__.cpython-312.pyc,,
stdnum/my/__pycache__/nric.cpython-312.pyc,,
stdnum/my/bp.dat,sha256=73O0wEkfgppiz-vTSHtYh6Bw8zMczsKE-jxxtW7Nt-Y,8155
stdnum/my/nric.py,sha256=Uob4eLn5o7z_aPemtr-ef0Aq7S98Jih7933DoXWIGzY,3741
stdnum/nl/__init__.py,sha256=iPmadcEPdGF81s2jKoQaYrrMfXhA77iR_Cr4HgGAKAw,1031
stdnum/nl/__pycache__/__init__.cpython-312.pyc,,
stdnum/nl/__pycache__/brin.cpython-312.pyc,,
stdnum/nl/__pycache__/bsn.cpython-312.pyc,,
stdnum/nl/__pycache__/btw.cpython-312.pyc,,
stdnum/nl/__pycache__/identiteitskaartnummer.cpython-312.pyc,,
stdnum/nl/__pycache__/onderwijsnummer.cpython-312.pyc,,
stdnum/nl/__pycache__/postcode.cpython-312.pyc,,
stdnum/nl/brin.py,sha256=NMJXoNHoxJote4OSvyFwh55hmXJNAK7fnTm747ieW0s,2764
stdnum/nl/bsn.py,sha256=ch86e5wI3cHVv0OSOJA63nTOZGlMSUUz7p2aSEnx0qI,2933
stdnum/nl/btw.py,sha256=9XgEtKEzzKoX7WDSVw3Oeb1HUFcinzMlkx_uA9Fj0Hs,3145
stdnum/nl/identiteitskaartnummer.py,sha256=4OpQuVIJhQePhg63JqdZFawExPmwF-B4Apm-wBd0aSc,2801
stdnum/nl/onderwijsnummer.py,sha256=Zjx5dwHemPRu12xBGKfp8kQD-slVbmxtNhaxCXXY2cs,2409
stdnum/nl/postcode.py,sha256=z8qmlcFqmSxvFkHkdFGmj4ov28voQuQvqDRtyfJMs_4,2554
stdnum/no/__init__.py,sha256=4n-OTHwj87FO23QX0Agq77J9Nsv4lckNT5ciI4E-5QA,1039
stdnum/no/__pycache__/__init__.cpython-312.pyc,,
stdnum/no/__pycache__/fodselsnummer.cpython-312.pyc,,
stdnum/no/__pycache__/iban.cpython-312.pyc,,
stdnum/no/__pycache__/kontonr.cpython-312.pyc,,
stdnum/no/__pycache__/mva.cpython-312.pyc,,
stdnum/no/__pycache__/orgnr.cpython-312.pyc,,
stdnum/no/fodselsnummer.py,sha256=w52-kDgvAsHgjIwESN7cbl1cZZ2KbeAkk8zYgNJHnUY,4829
stdnum/no/iban.py,sha256=MihcLEQ6dLsQXTFlvBrqDW2ukI95iyZUzklESbY6wuY,2526
stdnum/no/kontonr.py,sha256=AyYvuy0Z-yYq_Tr1MKixbKE8d4MmbOIRJ5DXkgzwOm4,3330
stdnum/no/mva.py,sha256=mnCcFig0-JZ25VnWbMjtRefwzzVYe2YP2f31v0objQ0,2336
stdnum/no/orgnr.py,sha256=nvmdyP2JazhRiPrIFwIFti4jEdOJgCkdnyh0RB3S4Zo,2645
stdnum/numdb.py,sha256=eTPplB7AH1Rb6ayDEF7iLBaekdsNUSFRAjEa1a5Z7TQ,7244
stdnum/nz/__init__.py,sha256=rTqHsaC_YJu6YNInnvkKFE41xZDD_FahABColp2szHM,978
stdnum/nz/__pycache__/__init__.cpython-312.pyc,,
stdnum/nz/__pycache__/bankaccount.cpython-312.pyc,,
stdnum/nz/__pycache__/ird.cpython-312.pyc,,
stdnum/nz/bankaccount.py,sha256=42qRExQBN-STWCvXvFutSg2ePo0QBcrRyhGsaqBl4Jk,5307
stdnum/nz/banks.dat,sha256=O6xgX7PBkdAQK_8xDzPeJog0yHvmil2sK9oM60-TEUk,70897
stdnum/nz/ird.py,sha256=x3K0Zw3071SwVbFYyx1OKeubfneI0Mxo8QUcSXbL20E,3361
stdnum/oui.dat,sha256=a6ZVS0-awwrAQMgL4Lu02hARGfs4yXPRw6HiFsPczIo,1181014
stdnum/pe/__init__.py,sha256=AS-Nmt4ip8BkqBrJU6KFoCb9uU0MmqQW8w7T1ZBrjxY,954
stdnum/pe/__pycache__/__init__.cpython-312.pyc,,
stdnum/pe/__pycache__/cui.cpython-312.pyc,,
stdnum/pe/__pycache__/ruc.cpython-312.pyc,,
stdnum/pe/cui.py,sha256=-w2bBS6wH-EiJDdBsUAIfd_RoMXboxuB4R8ItVpLmjo,3023
stdnum/pe/ruc.py,sha256=FSMfrysDqCFdYPOT-S3F7xmet2NIaL6IcIPz4iFJ-zg,2994
stdnum/pk/__init__.py,sha256=DcWim3F6Lvzx8ykEm7IAtgZ4XFkOgCDkMG__v__ixyU,872
stdnum/pk/__pycache__/__init__.cpython-312.pyc,,
stdnum/pk/__pycache__/cnic.cpython-312.pyc,,
stdnum/pk/cnic.py,sha256=NHbsw6gGKjP3ee4aH-OaKteQvItfMn331FU8blVvrPM,3406
stdnum/pl/__init__.py,sha256=iKXV2ArtSKmvkTdpRxaJMnjj0VP8lu03hZqplbOW3xs,976
stdnum/pl/__pycache__/__init__.cpython-312.pyc,,
stdnum/pl/__pycache__/nip.cpython-312.pyc,,
stdnum/pl/__pycache__/pesel.cpython-312.pyc,,
stdnum/pl/__pycache__/regon.cpython-312.pyc,,
stdnum/pl/nip.py,sha256=iVWQfcN9yCMOLAjFM7LjFww2eTmjAnGJl_Cxo6PjtgA,2566
stdnum/pl/pesel.py,sha256=6v2pvU-Cwiu4EriniXY8wTfPEf3uPh_tcqj_n3tOH0w,3712
stdnum/pl/regon.py,sha256=tFhkyqXlG9Jf1w8bctCxMs4J3yGIWNJo47IXGF3y-BA,3159
stdnum/pt/__init__.py,sha256=N7usTcQ0wZDhl_gBCmgjkK_3kcMadLDMXFH-f2KJHlA,984
stdnum/pt/__pycache__/__init__.cpython-312.pyc,,
stdnum/pt/__pycache__/cc.cpython-312.pyc,,
stdnum/pt/__pycache__/nif.cpython-312.pyc,,
stdnum/pt/cc.py,sha256=FQlKGZHxMwz9r0_mU9Fda5PPvcnJux0F4odlnF18Q5Q,3047
stdnum/pt/nif.py,sha256=1JPBt7Ievx5aFfJytoGysUSTGiX9Yw3bNykgB_kyVO4,2509
stdnum/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
stdnum/py/__init__.py,sha256=aYod7DeK0U2WIfY9LdmLSCVgF9d_ZDGzcWCdELQytF8,978
stdnum/py/__pycache__/__init__.cpython-312.pyc,,
stdnum/py/__pycache__/ruc.cpython-312.pyc,,
stdnum/py/ruc.py,sha256=_pAuTCBB9JOsFXYTm4G8IKTlIhj_ItDDFcQaLRHqBhY,3017
stdnum/ro/__init__.py,sha256=uRD_Bea-zivS3GeQOBGNacwj0JJ76jS548fQHH8IsQ4,979
stdnum/ro/__pycache__/__init__.cpython-312.pyc,,
stdnum/ro/__pycache__/cf.cpython-312.pyc,,
stdnum/ro/__pycache__/cnp.cpython-312.pyc,,
stdnum/ro/__pycache__/cui.cpython-312.pyc,,
stdnum/ro/__pycache__/onrc.cpython-312.pyc,,
stdnum/ro/cf.py,sha256=vhRPHU57TMDH-5D-_rUb8JkmOW7gdqWXDtpin1erTPw,2301
stdnum/ro/cnp.py,sha256=qA6_NzJSarwcPxhtrY2ovSIaBnpVcloN8cMpDoxgcNw,5248
stdnum/ro/cui.py,sha256=WEKDDl9rtijYHCFTmgcbXrYuUW7Qgc_5WuPRmaNN824,2979
stdnum/ro/onrc.py,sha256=XX3cEkBmkhxqyCzA0Q2pEEL4lYtUvuWvPmq-y37UmUQ,3414
stdnum/rs/__init__.py,sha256=ckipdsB-1ZG2xXmjppHjBn2uHGp4a7SjjMumFffeu3o,978
stdnum/rs/__pycache__/__init__.cpython-312.pyc,,
stdnum/rs/__pycache__/pib.cpython-312.pyc,,
stdnum/rs/pib.py,sha256=PqUmZ4Dx3llcJSSrV9lGN5Icy_tHf5hBvQFrUYXKLIM,2036
stdnum/ru/__init__.py,sha256=5FK2yz0v5h3u25HYcOHkizoCO6IclYTRfNwlQY8RvYg,978
stdnum/ru/__pycache__/__init__.cpython-312.pyc,,
stdnum/ru/__pycache__/inn.cpython-312.pyc,,
stdnum/ru/__pycache__/ogrn.cpython-312.pyc,,
stdnum/ru/inn.py,sha256=N7i9aNPKozR3cxT0Om7D9sYO_3U-K_7X_uZHOuzzr4M,3092
stdnum/ru/ogrn.py,sha256=7pu0X7tw1GyAQ--Bo3FWp3EvRVHSUkRs4vOXjOyYfRA,3350
stdnum/se/__init__.py,sha256=EWokMiFqv7H8-JFmUGgYdgA4aOqkrzjj9_60CBK4I-k,1048
stdnum/se/__pycache__/__init__.cpython-312.pyc,,
stdnum/se/__pycache__/orgnr.cpython-312.pyc,,
stdnum/se/__pycache__/personnummer.cpython-312.pyc,,
stdnum/se/__pycache__/postnummer.cpython-312.pyc,,
stdnum/se/__pycache__/vat.cpython-312.pyc,,
stdnum/se/orgnr.py,sha256=RfTGAeaQdkN-MJvDKaq_DoQX43rW8ZR6xs5fYBnKJAM,2448
stdnum/se/personnummer.py,sha256=CX-vUH4LDxz3QLaEM3M1A_Sb8Nzrf-RGCC8MhaKgkMw,3917
stdnum/se/postnummer.py,sha256=rU-iU9vxJhMqRaCYV5Z4ilyGNigT582AYuh35z-L3Qc,2431
stdnum/se/vat.py,sha256=8PPcEGwOmC1EM_mLFOpFqxaQwDHrQOqN4r0UGeIBtOE,2200
stdnum/sg/__init__.py,sha256=Tfivr-9c5kGuUZMH99X5XVykbmeV17sBK3hSNPV1zik,976
stdnum/sg/__pycache__/__init__.cpython-312.pyc,,
stdnum/sg/__pycache__/uen.cpython-312.pyc,,
stdnum/sg/uen.py,sha256=WqbNeFIQqZlGrpXscasAh_0uL1nB9O2abd-8Q4r-y3k,5983
stdnum/si/__init__.py,sha256=efxEHjVcFVGFnD3uOhyvC2brGLNCuinGBSPu4MmqFA0,1121
stdnum/si/__pycache__/__init__.cpython-312.pyc,,
stdnum/si/__pycache__/ddv.cpython-312.pyc,,
stdnum/si/__pycache__/emso.cpython-312.pyc,,
stdnum/si/__pycache__/maticna.cpython-312.pyc,,
stdnum/si/ddv.py,sha256=t9YtE_riuyuS4x4Z9fZ1FG7GPPURQu1QvGCuISB7MXM,2579
stdnum/si/emso.py,sha256=-KxuCsXzmi691EFUbIwnhqO2ZY0v-u5DkYDzmPl9U14,3669
stdnum/si/maticna.py,sha256=Wef7wXehbVbqVcJfFmju6Y2EkEBc0wxhX0UX6F7pud0,3261
stdnum/sk/__init__.py,sha256=YIzmZmvJCwueoMgVibb_L8R0PGj6lnoeM35YR6D7sOA,976
stdnum/sk/__pycache__/__init__.cpython-312.pyc,,
stdnum/sk/__pycache__/dph.cpython-312.pyc,,
stdnum/sk/__pycache__/rc.cpython-312.pyc,,
stdnum/sk/dph.py,sha256=nH0cj2a-GXeyy_te4aMhAje4pPLe0gz-ZtqvksVWR9o,2542
stdnum/sk/rc.py,sha256=Pf-gDygQZREEZrJz2e5E-yFM15Ow6hQs95tEE0XxYlo,1942
stdnum/sm/__init__.py,sha256=cxWURG0Cx7VB4yaKoutaacCobQUGhbI7kOmWAQSwaAo,984
stdnum/sm/__pycache__/__init__.cpython-312.pyc,,
stdnum/sm/__pycache__/coe.cpython-312.pyc,,
stdnum/sm/coe.py,sha256=tkcgryePzV5My2m-LwW9JYpRsxKuuz1GsoYeUut453I,2573
stdnum/sv/__init__.py,sha256=aONDAEWhm9qyHUZQxKId8_c_M5QhClC4D3cWs6CIgpM,980
stdnum/sv/__pycache__/__init__.cpython-312.pyc,,
stdnum/sv/__pycache__/nit.cpython-312.pyc,,
stdnum/sv/nit.py,sha256=h0EsmKmr_WYt6EmkSzWt0KWdnET6aossoQ764r2T8o0,4151
stdnum/th/__init__.py,sha256=ex6_cvEHVh4QgJ_5CXay9AiKPL7aTIVb_CYJpdJqThQ,967
stdnum/th/__pycache__/__init__.cpython-312.pyc,,
stdnum/th/__pycache__/moa.cpython-312.pyc,,
stdnum/th/__pycache__/pin.cpython-312.pyc,,
stdnum/th/__pycache__/tin.cpython-312.pyc,,
stdnum/th/moa.py,sha256=ugAmZ7jyLiqkHbBKSvNh15IF6dGzjOTyi3mmQCYwxrE,3067
stdnum/th/pin.py,sha256=Rpt-lz4g1BRMOxqSoZNhW6rwduFSmAX0Nf5gKY6GY4s,2906
stdnum/th/tin.py,sha256=pES0i09ESZ9kXJeOt_-tMqsMOGS1BmQtu_HqgA88_d4,2963
stdnum/tn/__init__.py,sha256=x1ifC1MWRbkuFQyIE1XMioesnv66VaRv6t8urX8mH9c,973
stdnum/tn/__pycache__/__init__.cpython-312.pyc,,
stdnum/tn/__pycache__/mf.cpython-312.pyc,,
stdnum/tn/mf.py,sha256=dFH_EuOx1-mJRoMZQmATcc8pyu9kSthdAJ46sBIiPKc,4327
stdnum/tr/__init__.py,sha256=oB-EVTRKd5-fJdF7gBhFntOprxwgZjAQGQydRGmFXUw,952
stdnum/tr/__pycache__/__init__.cpython-312.pyc,,
stdnum/tr/__pycache__/tckimlik.cpython-312.pyc,,
stdnum/tr/__pycache__/vkn.cpython-312.pyc,,
stdnum/tr/tckimlik.py,sha256=02xeppq6xPAyllkgl1ZFBRfm4ISsJLVFR88ZUxg9uGA,4343
stdnum/tr/vkn.py,sha256=gfIdSgqcn_81EIfMsVi0VBwA-g5GXG_bnSHSTlObCIQ,2680
stdnum/tw/__init__.py,sha256=E26oh8zfGB7bF-8mt5NSnbxfD7WihlN0WwEDkXC6dcM,976
stdnum/tw/__pycache__/__init__.cpython-312.pyc,,
stdnum/tw/__pycache__/ubn.cpython-312.pyc,,
stdnum/tw/ubn.py,sha256=a2A4H91mZZ5u9NraH2dTOuvKwS4nch_bFaPA2ii7NBY,2916
stdnum/ua/__init__.py,sha256=KfWle3iWiVnqmD3sTpcw9VJxQGi1PYJrYyyKcx8YaRA,874
stdnum/ua/__pycache__/__init__.cpython-312.pyc,,
stdnum/ua/__pycache__/edrpou.cpython-312.pyc,,
stdnum/ua/__pycache__/rntrc.cpython-312.pyc,,
stdnum/ua/edrpou.py,sha256=czquo-lq5BhSuTgQI2jKrEtxt7yYnua_bfcHlbph0CU,3133
stdnum/ua/rntrc.py,sha256=jSJ-22nE5bFgsn30XJzjJ1lJfq-hy0FUCnMaxWGwzbA,2809
stdnum/us/__init__.py,sha256=PpQA2ZV75PbafUYxNV7fnZvgS4lkmfZde1_NIHndKa8,880
stdnum/us/__pycache__/__init__.cpython-312.pyc,,
stdnum/us/__pycache__/atin.cpython-312.pyc,,
stdnum/us/__pycache__/ein.cpython-312.pyc,,
stdnum/us/__pycache__/itin.cpython-312.pyc,,
stdnum/us/__pycache__/ptin.cpython-312.pyc,,
stdnum/us/__pycache__/rtn.cpython-312.pyc,,
stdnum/us/__pycache__/ssn.cpython-312.pyc,,
stdnum/us/__pycache__/tin.cpython-312.pyc,,
stdnum/us/atin.py,sha256=aOdlY0gohnzmn1i6qCdH9gUu_9idTK08w-QVXon7ErE,2486
stdnum/us/ein.dat,sha256=Ul74M5TfxwcRhIFYNAP9hAdCTg5qaWkfp4_r9MdFA3E,648
stdnum/us/ein.py,sha256=3W64ApBIaEcm8sTXl0ekDN9GmLKb3jCkuiz1l4UOkg0,2949
stdnum/us/itin.py,sha256=iEunybgl_ilFSO9Iw1yvaC32z8WOAvPr6CsMBpHyCz8,3107
stdnum/us/ptin.py,sha256=-4K7t4TQObgQUajc2rx26LV8ZxOrgJqGwxvoXot7ofI,2140
stdnum/us/rtn.py,sha256=Q7NXHqIuFyAAmNiTyrkECuXmsoR4qTczipHwF_uAebc,2666
stdnum/us/ssn.py,sha256=mqjIz6iXtdQ4ce7hqiDzhKS4G5SjnXeFe5KXVmZ4l4g,3892
stdnum/us/tin.py,sha256=QUnKM8hUoUanpbMqagl10vd850nSyoemTwOx48WB2QY,3086
stdnum/util.py,sha256=2yVmaddIJd2pqRWsIQ63pD5oTcBW1eEQ5LaUNH34QwY,14588
stdnum/uy/__init__.py,sha256=f_a2lnZH7u5XtMnM-OXP8uA2kyjMkx6bruBI-1VfJ9Y,976
stdnum/uy/__pycache__/__init__.cpython-312.pyc,,
stdnum/uy/__pycache__/rut.cpython-312.pyc,,
stdnum/uy/rut.py,sha256=Mko0Li1ZnajYKYmjvRyWp8FiiD4_8pTVB3Es-1XyF7U,3619
stdnum/vatin.py,sha256=KCw5YJVV9or-QL9bXTL7rJ6I_uPr2KWIkS_3pjJETeg,3292
stdnum/ve/__init__.py,sha256=QtJU39vAJeXhW6kfzd9PnNerCp5kNlPfmemgQNnKfpk,976
stdnum/ve/__pycache__/__init__.cpython-312.pyc,,
stdnum/ve/__pycache__/rif.cpython-312.pyc,,
stdnum/ve/rif.py,sha256=7vDg2nXsc0OnjoWF-ni8iolukahhsuqNEamBzelrT1o,2925
stdnum/verhoeff.py,sha256=BhHlyyUEPSDyPsblilMOEtCl9-ErdJY0NftUawdSfj8,3659
stdnum/vn/__init__.py,sha256=kvbC8XSBePFb1sAD2vN9qSOrkc0TqvZEw9L9QLKENjQ,972
stdnum/vn/__pycache__/__init__.cpython-312.pyc,,
stdnum/vn/__pycache__/mst.cpython-312.pyc,,
stdnum/vn/mst.py,sha256=IsoSdHIZDWQtWgr9XFV67S3pUWlUJBvcbxJUIZLX69M,3705
stdnum/za/__init__.py,sha256=GujMEb1Q3xlP-20upOlMoXWJd1HraYMkaE9NFAsEwZA,880
stdnum/za/__pycache__/__init__.cpython-312.pyc,,
stdnum/za/__pycache__/idnr.cpython-312.pyc,,
stdnum/za/__pycache__/tin.cpython-312.pyc,,
stdnum/za/idnr.py,sha256=snA358JWa1j1onpbG1kQ-hyKKJpyXJnJ0L5QMcJdB3Q,3922
stdnum/za/tin.py,sha256=uw_-q9vYz6rJ_uN5sWUX_01yD4D_ahtYe2QzdsjkItI,2656
