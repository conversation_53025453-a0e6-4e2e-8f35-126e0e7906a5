{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://gkehub.googleapis.com/", "batchPath": "batch", "canonicalName": "GKE Hub", "description": "", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/anthos/multicluster-management/connect/registering-a-cluster", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "gkehub:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://gkehub.mtls.googleapis.com/", "name": "gkehub", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"organizations": {"resources": {"locations": {"resources": {"fleets": {"methods": {"list": {"description": "Returns all fleets within an organization or a project that the caller has access to.", "flatPath": "v1/organizations/{organizationsId}/locations/{locationsId}/fleets", "httpMethod": "GET", "id": "gkehub.organizations.locations.fleets.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of fleets to return. The service may return fewer than this value. If unspecified, at most 200 fleets will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListFleets` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListFleets` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The organization or project to list for Fleets under, in the format `organizations/*/locations/*` or `projects/*/locations/*`.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/fleets", "response": {"$ref": "ListFleetsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}, "projects": {"resources": {"locations": {"methods": {"get": {"description": "Gets information about a location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}", "httpMethod": "GET", "id": "gkehub.projects.locations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Resource name for the location.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Location"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists information about the supported locations for this service.", "flatPath": "v1/projects/{projectsId}/locations", "httpMethod": "GET", "id": "gkehub.projects.locations.list", "parameterOrder": ["name"], "parameters": {"extraLocationTypes": {"description": "Optional. A list of extra location types that should be used as conditions for controlling the visibility of the locations.", "location": "query", "repeated": true, "type": "string"}, "filter": {"description": "A filter to narrow down results to a preferred subset. The filtering language accepts strings like `\"displayName=tokyo\"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).", "location": "query", "type": "string"}, "name": {"description": "The resource that owns the locations collection, if applicable.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of results to return. If not set, the service selects a default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.", "location": "query", "type": "string"}}, "path": "v1/{+name}/locations", "response": {"$ref": "ListLocationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"features": {"methods": {"create": {"description": "Adds a new Feature.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/features", "httpMethod": "POST", "id": "gkehub.projects.locations.features.create", "parameterOrder": ["parent"], "parameters": {"featureId": {"description": "The ID of the feature to create.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent (project and location) where the Feature will be created. Specified in the format `projects/*/locations/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+parent}/features", "request": {"$ref": "Feature"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Removes a Feature.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/features/{featuresId}", "httpMethod": "DELETE", "id": "gkehub.projects.locations.features.delete", "parameterOrder": ["name"], "parameters": {"force": {"description": "If set to true, the delete will ignore any outstanding resources for this Feature (that is, `FeatureState.has_resources` is set to true). These resources will NOT be cleaned up or modified in any way.", "location": "query", "type": "boolean"}, "name": {"description": "Required. The Feature resource name in the format `projects/*/locations/*/features/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/features/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single Feature.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/features/{featuresId}", "httpMethod": "GET", "id": "gkehub.projects.locations.features.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The Feature resource name in the format `projects/*/locations/*/features/*`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/features/[^/]+$", "required": true, "type": "string"}, "returnPartialSuccess": {"description": "Optional. If set to true, the response will return partial results when some regions are unreachable and the unreachable field in Feature proto will be populated. If set to false, the request will fail when some regions are unreachable.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "response": {"$ref": "Feature"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/features/{featuresId}:getIamPolicy", "httpMethod": "GET", "id": "gkehub.projects.locations.features.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/features/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists Features in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/features", "httpMethod": "GET", "id": "gkehub.projects.locations.features.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Lists Features that match the filter expression, following the syntax outlined in https://google.aip.dev/160. Examples: - Feature with the name \"servicemesh\" in project \"foo-proj\": name = \"projects/foo-proj/locations/global/features/servicemesh\" - Features that have a label called `foo`: labels.foo:* - Features that have a label called `foo` whose value is `bar`: labels.foo = bar", "location": "query", "type": "string"}, "orderBy": {"description": "One or more fields to compare and use to sort the output. See https://google.aip.dev/132#ordering.", "location": "query", "type": "string"}, "pageSize": {"description": "When requesting a 'page' of resources, `page_size` specifies number of resources to return. If unspecified or set to 0, all resources will be returned.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "<PERSON><PERSON> returned by previous call to `ListFeatures` which specifies the position in the list from where to continue listing the resources.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent (project and location) where the Features will be listed. Specified in the format `projects/*/locations/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "returnPartialSuccess": {"description": "Optional. If set to true, the response will return partial results when some regions are unreachable and the unreachable field in Feature proto will be populated. If set to false, the request will fail when some regions are unreachable.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/features", "response": {"$ref": "ListFeaturesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates an existing Feature.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/features/{featuresId}", "httpMethod": "PATCH", "id": "gkehub.projects.locations.features.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The Feature resource name in the format `projects/*/locations/*/features/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/features/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Mask of fields to update.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "Feature"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/features/{featuresId}:setIamPolicy", "httpMethod": "POST", "id": "gkehub.projects.locations.features.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/features/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/features/{featuresId}:testIamPermissions", "httpMethod": "POST", "id": "gkehub.projects.locations.features.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/features/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "fleets": {"methods": {"create": {"description": "Creates a fleet.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/fleets", "httpMethod": "POST", "id": "gkehub.projects.locations.fleets.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent (project and location) where the Fleet will be created. Specified in the format `projects/*/locations/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/fleets", "request": {"$ref": "Fleet"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Removes a Fleet. There must be no memberships remaining in the Fleet.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/fleets/{fleetsId}", "httpMethod": "DELETE", "id": "gkehub.projects.locations.fleets.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The Fleet resource name in the format `projects/*/locations/*/fleets/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/fleets/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Returns the details of a fleet.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/fleets/{fleetsId}", "httpMethod": "GET", "id": "gkehub.projects.locations.fleets.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The Fleet resource name in the format `projects/*/locations/*/fleets/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/fleets/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Fleet"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Returns all fleets within an organization or a project that the caller has access to.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/fleets", "httpMethod": "GET", "id": "gkehub.projects.locations.fleets.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of fleets to return. The service may return fewer than this value. If unspecified, at most 200 fleets will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListFleets` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListFleets` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The organization or project to list for Fleets under, in the format `organizations/*/locations/*` or `projects/*/locations/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/fleets", "response": {"$ref": "ListFleetsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a fleet.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/fleets/{fleetsId}", "httpMethod": "PATCH", "id": "gkehub.projects.locations.fleets.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Output only. The full, unique resource name of this fleet in the format of `projects/{project}/locations/{location}/fleets/{fleet}`. Each Google Cloud project can have at most one fleet resource, named \"default\".", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/fleets/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The fields to be updated;", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "Fleet"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "memberships": {"methods": {"create": {"description": "Creates a new Membership. **This is currently only supported for GKE clusters on Google Cloud**. To register other clusters, follow the instructions at https://cloud.google.com/anthos/multicluster-management/connect/registering-a-cluster.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/memberships", "httpMethod": "POST", "id": "gkehub.projects.locations.memberships.create", "parameterOrder": ["parent"], "parameters": {"membershipId": {"description": "Required. Client chosen ID for the membership. `membership_id` must be a valid RFC 1123 compliant DNS label: 1. At most 63 characters in length 2. It must consist of lower case alphanumeric characters or `-` 3. It must start and end with an alphanumeric character Which can be expressed as the regex: `[a-z0-9]([-a-z0-9]*[a-z0-9])?`, with a maximum length of 63 characters.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent (project and location) where the Memberships will be created. Specified in the format `projects/*/locations/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+parent}/memberships", "request": {"$ref": "Membership"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Removes a Membership. **This is currently only supported for GKE clusters on Google Cloud**. To unregister other clusters, follow the instructions at https://cloud.google.com/anthos/multicluster-management/connect/unregistering-a-cluster.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/memberships/{membershipsId}", "httpMethod": "DELETE", "id": "gkehub.projects.locations.memberships.delete", "parameterOrder": ["name"], "parameters": {"force": {"description": "Optional. If set to true, any subresource from this Membership will also be deleted. Otherwise, the request will only work if the Membership has no subresource.", "location": "query", "type": "boolean"}, "name": {"description": "Required. The Membership resource name in the format `projects/*/locations/*/memberships/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/memberships/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "generateConnectManifest": {"description": "Generates the manifest for deployment of the GKE connect agent. **This method is used internally by Google-provided libraries.** Most clients should not need to call this method directly.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/memberships/{membershipsId}:generateConnectManifest", "httpMethod": "GET", "id": "gkehub.projects.locations.memberships.generateConnectManifest", "parameterOrder": ["name"], "parameters": {"imagePullSecretContent": {"description": "Optional. The image pull secret content for the registry, if not public.", "format": "byte", "location": "query", "type": "string"}, "isUpgrade": {"description": "Optional. If true, generate the resources for upgrade only. Some resources generated only for installation (e.g. secrets) will be excluded.", "location": "query", "type": "boolean"}, "name": {"description": "Required. The Membership resource name the Agent will associate with, in the format `projects/*/locations/*/memberships/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/memberships/[^/]+$", "required": true, "type": "string"}, "namespace": {"description": "Optional. Namespace for GKE Connect agent resources. Defaults to `gke-connect`. The Connect Agent is authorized automatically when run in the default namespace. Otherwise, explicit authorization must be granted with an additional IAM binding.", "location": "query", "type": "string"}, "proxy": {"description": "Optional. URI of a proxy if connectivity from the agent to gkeconnect.googleapis.com requires the use of a proxy. Format must be in the form `http(s)://{proxy_address}`, depending on the HTTP/HTTPS protocol supported by the proxy. This will direct the connect agent's outbound traffic through a HTTP(S) proxy.", "format": "byte", "location": "query", "type": "string"}, "registry": {"description": "Optional. The registry to fetch the connect agent image from. Defaults to gcr.io/gkeconnect.", "location": "query", "type": "string"}, "version": {"description": "Optional. The Connect agent version to use. Defaults to the most current version.", "location": "query", "type": "string"}}, "path": "v1/{+name}:generateConnectManifest", "response": {"$ref": "GenerateConnectManifestResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the details of a Membership.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/memberships/{membershipsId}", "httpMethod": "GET", "id": "gkehub.projects.locations.memberships.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The Membership resource name in the format `projects/*/locations/*/memberships/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/memberships/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Membership"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/memberships/{membershipsId}:getIamPolicy", "httpMethod": "GET", "id": "gkehub.projects.locations.memberships.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/memberships/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists Memberships in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/memberships", "httpMethod": "GET", "id": "gkehub.projects.locations.memberships.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Lists Memberships that match the filter expression, following the syntax outlined in https://google.aip.dev/160. Examples: - Name is `bar` in project `foo-proj` and location `global`: name = \"projects/foo-proj/locations/global/membership/bar\" - Memberships that have a label called `foo`: labels.foo:* - Memberships that have a label called `foo` whose value is `bar`: labels.foo = bar - Memberships in the CREATING state: state = CREATING", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. One or more fields to compare and use to sort the output. See https://google.aip.dev/132#ordering.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. When requesting a 'page' of resources, `page_size` specifies number of resources to return. If unspecified or set to 0, all resources will be returned.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. To<PERSON> returned by previous call to `ListMemberships` which specifies the position in the list from where to continue listing the resources.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent (project and location) where the Memberships will be listed. Specified in the format `projects/*/locations/*`. `projects/*/locations/-` list memberships in all the regions.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/memberships", "response": {"$ref": "ListMembershipsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates an existing Membership.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/memberships/{membershipsId}", "httpMethod": "PATCH", "id": "gkehub.projects.locations.memberships.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The Membership resource name in the format `projects/*/locations/*/memberships/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/memberships/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. A request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Mask of fields to update.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "Membership"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/memberships/{membershipsId}:setIamPolicy", "httpMethod": "POST", "id": "gkehub.projects.locations.memberships.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/memberships/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/memberships/{membershipsId}:testIamPermissions", "httpMethod": "POST", "id": "gkehub.projects.locations.memberships.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/memberships/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"bindings": {"methods": {"create": {"description": "Creates a MembershipBinding.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/memberships/{membershipsId}/bindings", "httpMethod": "POST", "id": "gkehub.projects.locations.memberships.bindings.create", "parameterOrder": ["parent"], "parameters": {"membershipBindingId": {"description": "Required. The ID to use for the MembershipBinding.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent (project and location) where the MembershipBinding will be created. Specified in the format `projects/*/locations/*/memberships/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/memberships/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/bindings", "request": {"$ref": "MembershipBinding"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a MembershipBinding.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/memberships/{membershipsId}/bindings/{bindingsId}", "httpMethod": "DELETE", "id": "gkehub.projects.locations.memberships.bindings.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The MembershipBinding resource name in the format `projects/*/locations/*/memberships/*/bindings/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/memberships/[^/]+/bindings/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Returns the details of a MembershipBinding.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/memberships/{membershipsId}/bindings/{bindingsId}", "httpMethod": "GET", "id": "gkehub.projects.locations.memberships.bindings.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The MembershipBinding resource name in the format `projects/*/locations/*/memberships/*/bindings/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/memberships/[^/]+/bindings/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "MembershipBinding"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists MembershipBindings.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/memberships/{membershipsId}/bindings", "httpMethod": "GET", "id": "gkehub.projects.locations.memberships.bindings.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Lists MembershipBindings that match the filter expression, following the syntax outlined in https://google.aip.dev/160.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. When requesting a 'page' of resources, `page_size` specifies number of resources to return. If unspecified or set to 0, all resources will be returned.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. To<PERSON> returned by previous call to `ListMembershipBindings` which specifies the position in the list from where to continue listing the resources.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent Membership for which the MembershipBindings will be listed. Specified in the format `projects/*/locations/*/memberships/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/memberships/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/bindings", "response": {"$ref": "ListMembershipBindingsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a MembershipBinding.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/memberships/{membershipsId}/bindings/{bindingsId}", "httpMethod": "PATCH", "id": "gkehub.projects.locations.memberships.bindings.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name for the membershipbinding itself `projects/{project}/locations/{location}/memberships/{membership}/bindings/{membershipbinding}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/memberships/[^/]+/bindings/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "MembershipBinding"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "rbacrolebindings": {"methods": {"create": {"description": "Creates a Membership RBACRoleBinding.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/memberships/{membershipsId}/rbacrolebindings", "httpMethod": "POST", "id": "gkehub.projects.locations.memberships.rbacrolebindings.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent (project and location) where the RBACRoleBinding will be created. Specified in the format `projects/*/locations/*/memberships/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/memberships/[^/]+$", "required": true, "type": "string"}, "rbacrolebindingId": {"description": "Required. Client chosen ID for the RBACRoleBinding. `rbacrolebinding_id` must be a valid RFC 1123 compliant DNS label: 1. At most 63 characters in length 2. It must consist of lower case alphanumeric characters or `-` 3. It must start and end with an alphanumeric character Which can be expressed as the regex: `[a-z0-9]([-a-z0-9]*[a-z0-9])?`, with a maximum length of 63 characters.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/rbacrolebindings", "request": {"$ref": "RBACRoleBinding"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a Membership RBACRoleBinding.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/memberships/{membershipsId}/rbacrolebindings/{rbacrolebindingsId}", "httpMethod": "DELETE", "id": "gkehub.projects.locations.memberships.rbacrolebindings.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The RBACRoleBinding resource name in the format `projects/*/locations/*/memberships/*/rbacrolebindings/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/memberships/[^/]+/rbacrolebindings/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "generateMembershipRBACRoleBindingYAML": {"description": "Generates a YAML of the RBAC policies for the specified RoleBinding and its associated impersonation resources.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/memberships/{membershipsId}/rbacrolebindings:generateMembershipRBACRoleBindingYAML", "httpMethod": "POST", "id": "gkehub.projects.locations.memberships.rbacrolebindings.generateMembershipRBACRoleBindingYAML", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent (project and location) where the RBACRoleBinding will be created. Specified in the format `projects/*/locations/*/memberships/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/memberships/[^/]+$", "required": true, "type": "string"}, "rbacrolebindingId": {"description": "Required. Client chosen ID for the RBACRoleBinding. `rbacrolebinding_id` must be a valid RFC 1123 compliant DNS label: 1. At most 63 characters in length 2. It must consist of lower case alphanumeric characters or `-` 3. It must start and end with an alphanumeric character Which can be expressed as the regex: `[a-z0-9]([-a-z0-9]*[a-z0-9])?`, with a maximum length of 63 characters.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/rbacrolebindings:generateMembershipRBACRoleBindingYAML", "request": {"$ref": "RBACRoleBinding"}, "response": {"$ref": "GenerateMembershipRBACRoleBindingYAMLResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Returns the details of a Membership RBACRoleBinding.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/memberships/{membershipsId}/rbacrolebindings/{rbacrolebindingsId}", "httpMethod": "GET", "id": "gkehub.projects.locations.memberships.rbacrolebindings.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The RBACRoleBinding resource name in the format `projects/*/locations/*/memberships/*/rbacrolebindings/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/memberships/[^/]+/rbacrolebindings/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "RBACRoleBinding"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all Membership RBACRoleBindings.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/memberships/{membershipsId}/rbacrolebindings", "httpMethod": "GET", "id": "gkehub.projects.locations.memberships.rbacrolebindings.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. When requesting a 'page' of resources, `page_size` specifies number of resources to return. If unspecified or set to 0, all resources will be returned.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. To<PERSON> returned by previous call to `ListMembershipRBACRoleBindings` which specifies the position in the list from where to continue listing the resources.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent (project and location) where the Features will be listed. Specified in the format `projects/*/locations/*/memberships/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/memberships/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/rbacrolebindings", "response": {"$ref": "ListMembershipRBACRoleBindingsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a Membership RBACRoleBinding.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/memberships/{membershipsId}/rbacrolebindings/{rbacrolebindingsId}", "httpMethod": "PATCH", "id": "gkehub.projects.locations.memberships.rbacrolebindings.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name for the rbacrolebinding `projects/{project}/locations/{location}/scopes/{scope}/rbacrolebindings/{rbacrolebinding}` or `projects/{project}/locations/{location}/memberships/{membership}/rbacrolebindings/{rbacrolebinding}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/memberships/[^/]+/rbacrolebindings/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "RBACRoleBinding"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "gkehub.projects.locations.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:cancel", "request": {"$ref": "CancelOperationRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "DELETE", "id": "gkehub.projects.locations.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "GET", "id": "gkehub.projects.locations.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations", "httpMethod": "GET", "id": "gkehub.projects.locations.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1/{+name}/operations", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "scopes": {"methods": {"create": {"description": "Creates a Scope.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/scopes", "httpMethod": "POST", "id": "gkehub.projects.locations.scopes.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent (project and location) where the Scope will be created. Specified in the format `projects/*/locations/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "scopeId": {"description": "Required. Client chosen ID for the Scope. `scope_id` must be a ????", "location": "query", "type": "string"}}, "path": "v1/{+parent}/scopes", "request": {"$ref": "<PERSON><PERSON>"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a Scope.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/scopes/{scopesId}", "httpMethod": "DELETE", "id": "gkehub.projects.locations.scopes.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The Scope resource name in the format `projects/*/locations/*/scopes/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/scopes/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Returns the details of a Scope.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/scopes/{scopesId}", "httpMethod": "GET", "id": "gkehub.projects.locations.scopes.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The Scope resource name in the format `projects/*/locations/*/scopes/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/scopes/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "<PERSON><PERSON>"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/scopes/{scopesId}:getIamPolicy", "httpMethod": "GET", "id": "gkehub.projects.locations.scopes.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/scopes/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists Scopes.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/scopes", "httpMethod": "GET", "id": "gkehub.projects.locations.scopes.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. When requesting a 'page' of resources, `page_size` specifies number of resources to return. If unspecified or set to 0, all resources will be returned.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. To<PERSON> returned by previous call to `ListScopes` which specifies the position in the list from where to continue listing the resources.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent (project and location) where the Scope will be listed. Specified in the format `projects/*/locations/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/scopes", "response": {"$ref": "ListScopesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "listMemberships": {"description": "Lists Memberships bound to a Scope. The response includes relevant Memberships from all regions.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/scopes/{scopesId}:listMemberships", "httpMethod": "GET", "id": "gkehub.projects.locations.scopes.listMemberships", "parameterOrder": ["scopeName"], "parameters": {"filter": {"description": "Optional. Lists Memberships that match the filter expression, following the syntax outlined in https://google.aip.dev/160. Currently, filtering can be done only based on Memberships's `name`, `labels`, `create_time`, `update_time`, and `unique_id`.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. When requesting a 'page' of resources, `page_size` specifies number of resources to return. If unspecified or set to 0, all resources will be returned. Pagination is currently not supported; therefore, setting this field does not have any impact for now.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. Token returned by previous call to `ListBoundMemberships` which specifies the position in the list from where to continue listing the resources.", "location": "query", "type": "string"}, "scopeName": {"description": "Required. Name of the Scope, in the format `projects/*/locations/global/scopes/*`, to which the Memberships are bound.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/scopes/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+scopeName}:listMemberships", "response": {"$ref": "ListBoundMembershipsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "listPermitted": {"description": "Lists permitted Scopes.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/scopes:listPermitted", "httpMethod": "GET", "id": "gkehub.projects.locations.scopes.listPermitted", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. When requesting a 'page' of resources, `page_size` specifies number of resources to return. If unspecified or set to 0, all resources will be returned.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. To<PERSON> returned by previous call to `ListPermittedScopes` which specifies the position in the list from where to continue listing the resources.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent (project and location) where the Scope will be listed. Specified in the format `projects/*/locations/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/scopes:listPermitted", "response": {"$ref": "ListPermittedScopesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a scopes.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/scopes/{scopesId}", "httpMethod": "PATCH", "id": "gkehub.projects.locations.scopes.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name for the scope `projects/{project}/locations/{location}/scopes/{scope}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/scopes/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "<PERSON><PERSON>"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/scopes/{scopesId}:setIamPolicy", "httpMethod": "POST", "id": "gkehub.projects.locations.scopes.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/scopes/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/scopes/{scopesId}:testIamPermissions", "httpMethod": "POST", "id": "gkehub.projects.locations.scopes.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/scopes/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"namespaces": {"methods": {"create": {"description": "Creates a fleet namespace.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/scopes/{scopesId}/namespaces", "httpMethod": "POST", "id": "gkehub.projects.locations.scopes.namespaces.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent (project and location) where the Namespace will be created. Specified in the format `projects/*/locations/*/scopes/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/scopes/[^/]+$", "required": true, "type": "string"}, "scopeNamespaceId": {"description": "Required. Client chosen ID for the Namespace. `namespace_id` must be a valid RFC 1123 compliant DNS label: 1. At most 63 characters in length 2. It must consist of lower case alphanumeric characters or `-` 3. It must start and end with an alphanumeric character Which can be expressed as the regex: `[a-z0-9]([-a-z0-9]*[a-z0-9])?`, with a maximum length of 63 characters.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/namespaces", "request": {"$ref": "Namespace"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a fleet namespace.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/scopes/{scopesId}/namespaces/{namespacesId}", "httpMethod": "DELETE", "id": "gkehub.projects.locations.scopes.namespaces.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The Namespace resource name in the format `projects/*/locations/*/scopes/*/namespaces/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/scopes/[^/]+/namespaces/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Returns the details of a fleet namespace.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/scopes/{scopesId}/namespaces/{namespacesId}", "httpMethod": "GET", "id": "gkehub.projects.locations.scopes.namespaces.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The Namespace resource name in the format `projects/*/locations/*/scopes/*/namespaces/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/scopes/[^/]+/namespaces/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Namespace"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists fleet namespaces.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/scopes/{scopesId}/namespaces", "httpMethod": "GET", "id": "gkehub.projects.locations.scopes.namespaces.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. When requesting a 'page' of resources, `page_size` specifies number of resources to return. If unspecified or set to 0, all resources will be returned.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. Token returned by previous call to `ListFeatures` which specifies the position in the list from where to continue listing the resources.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent (project and location) where the Features will be listed. Specified in the format `projects/*/locations/*/scopes/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/scopes/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/namespaces", "response": {"$ref": "ListScopeNamespacesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a fleet namespace.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/scopes/{scopesId}/namespaces/{namespacesId}", "httpMethod": "PATCH", "id": "gkehub.projects.locations.scopes.namespaces.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name for the namespace `projects/{project}/locations/{location}/namespaces/{namespace}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/scopes/[^/]+/namespaces/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "Namespace"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "rbacrolebindings": {"methods": {"create": {"description": "Creates a Scope RBACRoleBinding.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/scopes/{scopesId}/rbacrolebindings", "httpMethod": "POST", "id": "gkehub.projects.locations.scopes.rbacrolebindings.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent (project and location) where the RBACRoleBinding will be created. Specified in the format `projects/*/locations/*/scopes/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/scopes/[^/]+$", "required": true, "type": "string"}, "rbacrolebindingId": {"description": "Required. Client chosen ID for the RBACRoleBinding. `rbacrolebinding_id` must be a valid RFC 1123 compliant DNS label: 1. At most 63 characters in length 2. It must consist of lower case alphanumeric characters or `-` 3. It must start and end with an alphanumeric character Which can be expressed as the regex: `[a-z0-9]([-a-z0-9]*[a-z0-9])?`, with a maximum length of 63 characters.", "location": "query", "type": "string"}}, "path": "v1/{+parent}/rbacrolebindings", "request": {"$ref": "RBACRoleBinding"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a Scope RBACRoleBinding.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/scopes/{scopesId}/rbacrolebindings/{rbacrolebindingsId}", "httpMethod": "DELETE", "id": "gkehub.projects.locations.scopes.rbacrolebindings.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The RBACRoleBinding resource name in the format `projects/*/locations/*/scopes/*/rbacrolebindings/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/scopes/[^/]+/rbacrolebindings/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Returns the details of a Scope RBACRoleBinding.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/scopes/{scopesId}/rbacrolebindings/{rbacrolebindingsId}", "httpMethod": "GET", "id": "gkehub.projects.locations.scopes.rbacrolebindings.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The RBACRoleBinding resource name in the format `projects/*/locations/*/scopes/*/rbacrolebindings/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/scopes/[^/]+/rbacrolebindings/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "RBACRoleBinding"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists all Scope RBACRoleBindings.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/scopes/{scopesId}/rbacrolebindings", "httpMethod": "GET", "id": "gkehub.projects.locations.scopes.rbacrolebindings.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. When requesting a 'page' of resources, `page_size` specifies number of resources to return. If unspecified or set to 0, all resources will be returned.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. To<PERSON> returned by previous call to `ListScopeRBACRoleBindings` which specifies the position in the list from where to continue listing the resources.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent (project and location) where the Features will be listed. Specified in the format `projects/*/locations/*/scopes/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/scopes/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/rbacrolebindings", "response": {"$ref": "ListScopeRBACRoleBindingsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a Scope RBACRoleBinding.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/scopes/{scopesId}/rbacrolebindings/{rbacrolebindingsId}", "httpMethod": "PATCH", "id": "gkehub.projects.locations.scopes.rbacrolebindings.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name for the rbacrolebinding `projects/{project}/locations/{location}/scopes/{scope}/rbacrolebindings/{rbacrolebinding}` or `projects/{project}/locations/{location}/memberships/{membership}/rbacrolebindings/{rbacrolebinding}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/scopes/[^/]+/rbacrolebindings/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The fields to be updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "RBACRoleBinding"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}}}, "revision": "20250516", "rootUrl": "https://gkehub.googleapis.com/", "schemas": {"AppDevExperienceFeatureSpec": {"description": "Spec for App Dev Experience Feature.", "id": "AppDevExperienceFeatureSpec", "properties": {}, "type": "object"}, "AppDevExperienceFeatureState": {"description": "State for App Dev Exp Feature.", "id": "AppDevExperienceFeatureState", "properties": {"networkingInstallSucceeded": {"$ref": "Status", "description": "Status of subcomponent that detects configured Service Mesh resources."}}, "type": "object"}, "ApplianceCluster": {"description": "ApplianceCluster contains information specific to GDC Edge Appliance Clusters.", "id": "ApplianceCluster", "properties": {"resourceLink": {"description": "Immutable. Self-link of the Google Cloud resource for the Appliance Cluster. For example: //transferappliance.googleapis.com/projects/my-project/locations/us-west1-a/appliances/my-appliance", "type": "string"}}, "type": "object"}, "AuditConfig": {"description": "Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { \"audit_configs\": [ { \"service\": \"allServices\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" }, { \"log_type\": \"ADMIN_READ\" } ] }, { \"service\": \"sampleservice.googleapis.com\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\" }, { \"log_type\": \"DATA_WRITE\", \"exempted_members\": [ \"user:<EMAIL>\" ] } ] } ] } For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.", "id": "AuditConfig", "properties": {"auditLogConfigs": {"description": "The configuration for logging of each type of permission.", "items": {"$ref": "AuditLogConfig"}, "type": "array"}, "service": {"description": "Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.", "type": "string"}}, "type": "object"}, "AuditLogConfig": {"description": "Provides the configuration for logging a type of permissions. Example: { \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" } ] } This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.", "id": "AuditLogConfig", "properties": {"exemptedMembers": {"description": "Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.", "items": {"type": "string"}, "type": "array"}, "logType": {"description": "The log type that this config enables.", "enum": ["LOG_TYPE_UNSPECIFIED", "ADMIN_READ", "DATA_WRITE", "DATA_READ"], "enumDescriptions": ["Default case. Should never be this.", "Admin reads. Example: CloudIAM getIamPolicy", "Data writes. Example: CloudSQL Users create", "Data reads. Example: CloudSQL Users list"], "type": "string"}}, "type": "object"}, "Authority": {"description": "Authority encodes how Google will recognize identities from this Membership. See the workload identity documentation for more details: https://cloud.google.com/kubernetes-engine/docs/how-to/workload-identity", "id": "Authority", "properties": {"identityProvider": {"description": "Output only. An identity provider that reflects the `issuer` in the workload identity pool.", "readOnly": true, "type": "string"}, "issuer": {"description": "Optional. A JSON Web Token (JWT) issuer URI. `issuer` must start with `https://` and be a valid URL with length <2000 characters, it must use `location` rather than `zone` for GKE clusters. If set, then Google will allow valid OIDC tokens from this issuer to authenticate within the workload_identity_pool. OIDC discovery will be performed on this URI to validate tokens from the issuer. Clearing `issuer` disables Workload Identity. `issuer` cannot be directly modified; it must be cleared (and Workload Identity disabled) before using a new issuer (and re-enabling Workload Identity).", "type": "string"}, "oidcJwks": {"description": "Optional. OIDC verification keys for this Membership in JWKS format (RFC 7517). When this field is set, OIDC discovery will NOT be performed on `issuer`, and instead OIDC tokens will be validated using this field.", "format": "byte", "type": "string"}, "scopeTenancyIdentityProvider": {"description": "Optional. Output only. The identity provider for the scope-tenancy workload identity pool.", "readOnly": true, "type": "string"}, "scopeTenancyWorkloadIdentityPool": {"description": "Optional. Output only. The name of the scope-tenancy workload identity pool. This pool is set in the fleet-level feature.", "readOnly": true, "type": "string"}, "workloadIdentityPool": {"description": "Output only. The name of the workload identity pool in which `issuer` will be recognized. There is a single Workload Identity Pool per Hub that is shared between all Memberships that belong to that Hub. For a Hub hosted in {PROJECT_ID}, the workload pool format is `{PROJECT_ID}.hub.id.goog`, although this is subject to change in newer versions of this API.", "readOnly": true, "type": "string"}}, "type": "object"}, "BinaryAuthorizationConfig": {"description": "BinaryAuthorizationConfig defines the fleet level configuration of binary authorization feature.", "id": "BinaryAuthorizationConfig", "properties": {"evaluationMode": {"description": "Optional. Mode of operation for binauthz policy evaluation.", "enum": ["EVALUATION_MODE_UNSPECIFIED", "DISABLED", "POLICY_BINDINGS"], "enumDescriptions": ["Default value", "Disable BinaryAuthorization", "Use Binary Authorization with the policies specified in policy_bindings."], "type": "string"}, "policyBindings": {"description": "Optional. Binauthz policies that apply to this cluster.", "items": {"$ref": "PolicyBinding"}, "type": "array"}}, "type": "object"}, "Binding": {"description": "Associates `members`, or principals, with a `role`.", "id": "Binding", "properties": {"condition": {"$ref": "Expr", "description": "The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies)."}, "members": {"description": "Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workforce identity pool. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`: All workforce identities in a group. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All workforce identities with a specific attribute value. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/*`: All identities in a workforce identity pool. * `principal://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workload identity pool. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/group/{group_id}`: A workload identity pool group. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All identities in a workload identity pool with a certain attribute. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/*`: All identities in a workload identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid}` and the recovered group retains the role in the binding. * `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: Deleted single identity in a workforce identity pool. For example, `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value`.", "items": {"type": "string"}, "type": "array"}, "role": {"description": "Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview of the IAM roles and permissions, see the [IAM documentation](https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see [here](https://cloud.google.com/iam/docs/understanding-roles).", "type": "string"}}, "type": "object"}, "CancelOperationRequest": {"description": "The request message for Operations.CancelOperation.", "id": "CancelOperationRequest", "properties": {}, "type": "object"}, "ClusterUpgradeFleetSpec": {"description": "**ClusterUpgrade**: The configuration for the fleet-level ClusterUpgrade feature.", "id": "ClusterUpgradeFleetSpec", "properties": {"gkeUpgradeOverrides": {"description": "Allow users to override some properties of each GKE upgrade.", "items": {"$ref": "ClusterUpgradeGKEUpgradeOverride"}, "type": "array"}, "postConditions": {"$ref": "ClusterUpgradePostConditions", "description": "Required. Post conditions to evaluate to mark an upgrade COMPLETE. Required."}, "upstreamFleets": {"description": "This fleet consumes upgrades that have COMPLETE status code in the upstream fleets. See UpgradeStatus.Code for code definitions. The fleet name should be either fleet project number or id. This is defined as repeated for future proof reasons. Initial implementation will enforce at most one upstream fleet.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ClusterUpgradeFleetState": {"description": "**ClusterUpgrade**: The state for the fleet-level ClusterUpgrade feature.", "id": "ClusterUpgradeFleetState", "properties": {"downstreamFleets": {"description": "This fleets whose upstream_fleets contain the current fleet. The fleet name should be either fleet project number or id.", "items": {"type": "string"}, "type": "array"}, "gkeState": {"$ref": "ClusterUpgradeGKEUpgradeFeatureState", "description": "Feature state for GKE clusters."}, "ignored": {"additionalProperties": {"$ref": "ClusterUpgradeIgnoredMembership"}, "description": "A list of memberships ignored by the feature. For example, manually upgraded clusters can be ignored if they are newer than the default versions of its release channel. The membership resource is in the format: `projects/{p}/locations/{l}/membership/{m}`.", "type": "object"}}, "type": "object"}, "ClusterUpgradeGKEUpgrade": {"description": "GKEUpgrade represents a GKE provided upgrade, e.g., control plane upgrade.", "id": "ClusterUpgradeGKEUpgrade", "properties": {"name": {"description": "Name of the upgrade, e.g., \"k8s_control_plane\". It should be a valid upgrade name. It must not exceet 99 characters.", "type": "string"}, "version": {"description": "Version of the upgrade, e.g., \"1.22.1-gke.100\". It should be a valid version. It must not exceet 99 characters.", "type": "string"}}, "type": "object"}, "ClusterUpgradeGKEUpgradeFeatureCondition": {"description": "GKEUpgradeFeatureCondition describes the condition of the feature for GKE clusters at a certain point of time.", "id": "ClusterUpgradeGKEUpgradeFeatureCondition", "properties": {"reason": {"description": "Reason why the feature is in this status.", "type": "string"}, "status": {"description": "Status of the condition, one of True, False, Unknown.", "type": "string"}, "type": {"description": "Type of the condition, for example, \"ready\".", "type": "string"}, "updateTime": {"description": "Last timestamp the condition was updated.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "ClusterUpgradeGKEUpgradeFeatureState": {"description": "GKEUpgradeFeatureState contains feature states for GKE clusters in the scope.", "id": "ClusterUpgradeGKEUpgradeFeatureState", "properties": {"conditions": {"description": "Current conditions of the feature.", "items": {"$ref": "ClusterUpgradeGKEUpgradeFeatureCondition"}, "type": "array"}, "upgradeState": {"description": "Upgrade state. It will eventually replace `state`.", "items": {"$ref": "ClusterUpgradeGKEUpgradeState"}, "type": "array"}}, "type": "object"}, "ClusterUpgradeGKEUpgradeOverride": {"description": "Properties of a GKE upgrade that can be overridden by the user. For example, a user can skip soaking by overriding the soaking to 0.", "id": "ClusterUpgradeGKEUpgradeOverride", "properties": {"postConditions": {"$ref": "ClusterUpgradePostConditions", "description": "Required. Post conditions to override for the specified upgrade (name + version). Required."}, "upgrade": {"$ref": "ClusterUpgradeGKEUpgrade", "description": "Required. Which upgrade to override. Required."}}, "type": "object"}, "ClusterUpgradeGKEUpgradeState": {"description": "GKEUpgradeState is a GKEUpgrade and its state at the scope and fleet level.", "id": "ClusterUpgradeGKEUpgradeState", "properties": {"stats": {"additionalProperties": {"format": "int64", "type": "string"}, "description": "Number of GKE clusters in each status code.", "type": "object"}, "status": {"$ref": "ClusterUpgradeUpgradeStatus", "description": "Status of the upgrade."}, "upgrade": {"$ref": "ClusterUpgradeGKEUpgrade", "description": "Which upgrade to track the state."}}, "type": "object"}, "ClusterUpgradeIgnoredMembership": {"description": "IgnoredMembership represents a membership ignored by the feature. A membership can be ignored because it was manually upgraded to a newer version than RC default.", "id": "ClusterUpgradeIgnoredMembership", "properties": {"ignoredTime": {"description": "Time when the membership was first set to ignored.", "format": "google-datetime", "type": "string"}, "reason": {"description": "Reason why the membership is ignored.", "type": "string"}}, "type": "object"}, "ClusterUpgradeMembershipGKEUpgradeState": {"description": "ScopeGKEUpgradeState is a GKEUpgrade and its state per-membership.", "id": "ClusterUpgradeMembershipGKEUpgradeState", "properties": {"status": {"$ref": "ClusterUpgradeUpgradeStatus", "description": "Status of the upgrade."}, "upgrade": {"$ref": "ClusterUpgradeGKEUpgrade", "description": "Which upgrade to track the state."}}, "type": "object"}, "ClusterUpgradeMembershipState": {"description": "Per-membership state for this feature.", "id": "ClusterUpgradeMembershipState", "properties": {"ignored": {"$ref": "ClusterUpgradeIgnoredMembership", "description": "Whether this membership is ignored by the feature. For example, manually upgraded clusters can be ignored if they are newer than the default versions of its release channel."}, "upgrades": {"description": "Actual upgrade state against desired.", "items": {"$ref": "ClusterUpgradeMembershipGKEUpgradeState"}, "type": "array"}}, "type": "object"}, "ClusterUpgradePostConditions": {"description": "Post conditional checks after an upgrade has been applied on all eligible clusters.", "id": "ClusterUpgradePostConditions", "properties": {"soaking": {"description": "Required. Amount of time to \"soak\" after a rollout has been finished before marking it COMPLETE. Cannot exceed 30 days. Required.", "format": "google-duration", "type": "string"}}, "type": "object"}, "ClusterUpgradeUpgradeStatus": {"description": "UpgradeStatus provides status information for each upgrade.", "id": "ClusterUpgradeUpgradeStatus", "properties": {"code": {"description": "Status code of the upgrade.", "enum": ["CODE_UNSPECIFIED", "INELIGIBLE", "PENDING", "IN_PROGRESS", "SOAKING", "FORCED_SOAKING", "COMPLETE"], "enumDescriptions": ["Required by https://linter.aip.dev/126/unspecified.", "The upgrade is ineligible. At the scope level, this means the upgrade is ineligible for all the clusters in the scope.", "The upgrade is pending. At the scope level, this means the upgrade is pending for all the clusters in the scope.", "The upgrade is in progress. At the scope level, this means the upgrade is in progress for at least one cluster in the scope.", "The upgrade has finished and is soaking until the soaking time is up. At the scope level, this means at least one cluster is in soaking while the rest are either soaking or complete.", "A cluster will be forced to enter soaking if an upgrade doesn't finish within a certain limit, despite it's actual status.", "The upgrade has passed all post conditions (soaking). At the scope level, this means all eligible clusters are in COMPLETE status."], "type": "string"}, "reason": {"description": "Reason for this status.", "type": "string"}, "updateTime": {"description": "Last timestamp the status was updated.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "CommonFeatureSpec": {"description": "CommonFeatureSpec contains Fleet-wide configuration information", "id": "CommonFeatureSpec", "properties": {"appdevexperience": {"$ref": "AppDevExperienceFeatureSpec", "description": "Appdevexperience specific spec."}, "clusterupgrade": {"$ref": "ClusterUpgradeFleetSpec", "description": "ClusterUpgrade (fleet-based) feature spec."}, "dataplanev2": {"$ref": "DataplaneV2FeatureSpec", "description": "DataplaneV2 feature spec."}, "fleetobservability": {"$ref": "FleetObservabilityFeatureSpec", "description": "FleetObservability feature spec."}, "multiclusteringress": {"$ref": "MultiClusterIngressFeatureSpec", "description": "Multicluster Ingress-specific spec."}, "rbacrolebindingactuation": {"$ref": "RBACRoleBindingActuationFeatureSpec", "description": "RBAC Role Binding Actuation feature spec"}}, "type": "object"}, "CommonFeatureState": {"description": "CommonFeatureState contains Fleet-wide Feature status information.", "id": "CommonFeatureState", "properties": {"appdevexperience": {"$ref": "AppDevExperienceFeatureState", "description": "Appdevexperience specific state."}, "clusterupgrade": {"$ref": "ClusterUpgradeFleetState", "description": "ClusterUpgrade fleet-level state."}, "fleetobservability": {"$ref": "FleetObservabilityFeatureState", "description": "FleetObservability feature state."}, "rbacrolebindingactuation": {"$ref": "RBACRoleBindingActuationFeatureState", "description": "RBAC Role Binding Actuation feature state"}, "state": {"$ref": "FeatureState", "description": "Output only. The \"running state\" of the Feature in this Fleet.", "readOnly": true}}, "type": "object"}, "CommonFleetDefaultMemberConfigSpec": {"description": "CommonFleetDefaultMemberConfigSpec contains default configuration information for memberships of a fleet", "id": "CommonFleetDefaultMemberConfigSpec", "properties": {"configmanagement": {"$ref": "ConfigManagementMembershipSpec", "description": "Config Management-specific spec."}, "identityservice": {"$ref": "IdentityServiceMembershipSpec", "description": "Identity Service-specific spec."}, "mesh": {"$ref": "ServiceMeshMembershipSpec", "description": "Anthos Service Mesh-specific spec"}, "policycontroller": {"$ref": "PolicyControllerMembershipSpec", "description": "Policy Controller spec."}}, "type": "object"}, "CompliancePostureConfig": {"description": "CompliancePostureConfig defines the settings needed to enable/disable features for the Compliance Posture.", "id": "CompliancePostureConfig", "properties": {"complianceStandards": {"description": "List of enabled compliance standards.", "items": {"$ref": "ComplianceStandard"}, "type": "array"}, "mode": {"description": "Defines the enablement mode for Compliance Posture.", "enum": ["MODE_UNSPECIFIED", "DISABLED", "ENABLED"], "enumDescriptions": ["Default value not specified.", "Disables Compliance Posture features on the cluster.", "Enables Compliance Posture features on the cluster."], "type": "string"}}, "type": "object"}, "ComplianceStandard": {"id": "ComplianceStandard", "properties": {"standard": {"description": "Name of the compliance standard.", "type": "string"}}, "type": "object"}, "ConfigManagementConfigSync": {"description": "Configuration for Config Sync", "id": "ConfigManagementConfigSync", "properties": {"deploymentOverrides": {"description": "Optional. Configuration for deployment overrides.", "items": {"$ref": "ConfigManagementDeploymentOverride"}, "type": "array"}, "enabled": {"description": "Optional. Enables the installation of ConfigSync. If set to true, ConfigSync resources will be created and the other ConfigSync fields will be applied if exist. If set to false, all other ConfigSync fields will be ignored, ConfigSync resources will be deleted. If omitted, ConfigSync resources will be managed depends on the presence of the git or oci field.", "type": "boolean"}, "git": {"$ref": "ConfigManagementGitConfig", "description": "Optional. Git repo configuration for the cluster."}, "metricsGcpServiceAccountEmail": {"deprecated": true, "description": "Optional. The Email of the Google Cloud Service Account (GSA) used for exporting Config Sync metrics to Cloud Monitoring and Cloud Monarch when Workload Identity is enabled. The GSA should have the Monitoring Metric Writer (roles/monitoring.metricWriter) IAM role. The Kubernetes ServiceAccount `default` in the namespace `config-management-monitoring` should be bound to the GSA. Deprecated: If Workload Identity Federation for GKE is enabled, Google Cloud Service Account is no longer needed for exporting Config Sync metrics: https://cloud.google.com/kubernetes-engine/enterprise/config-sync/docs/how-to/monitor-config-sync-cloud-monitoring#custom-monitoring.", "type": "string"}, "oci": {"$ref": "ConfigManagementOciConfig", "description": "Optional. OCI repo configuration for the cluster"}, "preventDrift": {"description": "Optional. Set to true to enable the Config Sync admission webhook to prevent drifts. If set to `false`, disables the Config Sync admission webhook and does not prevent drifts.", "type": "boolean"}, "sourceFormat": {"description": "Optional. Specifies whether the Config Sync Repo is in \"hierarchical\" or \"unstructured\" mode.", "type": "string"}, "stopSyncing": {"description": "Optional. Set to true to stop syncing configs for a single cluster. Default to false.", "type": "boolean"}}, "type": "object"}, "ConfigManagementConfigSyncDeploymentState": {"description": "The state of ConfigSync's deployment on a cluster", "id": "ConfigManagementConfigSyncDeploymentState", "properties": {"admissionWebhook": {"description": "Deployment state of admission-webhook", "enum": ["DEPLOYMENT_STATE_UNSPECIFIED", "NOT_INSTALLED", "INSTALLED", "ERROR", "PENDING"], "enumDescriptions": ["Deployment's state cannot be determined", "Deployment is not installed", "Deployment is installed", "Deployment was attempted to be installed, but has errors", "Deployment is installing or terminating"], "type": "string"}, "gitSync": {"description": "Deployment state of the git-sync pod", "enum": ["DEPLOYMENT_STATE_UNSPECIFIED", "NOT_INSTALLED", "INSTALLED", "ERROR", "PENDING"], "enumDescriptions": ["Deployment's state cannot be determined", "Deployment is not installed", "Deployment is installed", "Deployment was attempted to be installed, but has errors", "Deployment is installing or terminating"], "type": "string"}, "importer": {"description": "Deployment state of the importer pod", "enum": ["DEPLOYMENT_STATE_UNSPECIFIED", "NOT_INSTALLED", "INSTALLED", "ERROR", "PENDING"], "enumDescriptions": ["Deployment's state cannot be determined", "Deployment is not installed", "Deployment is installed", "Deployment was attempted to be installed, but has errors", "Deployment is installing or terminating"], "type": "string"}, "monitor": {"description": "Deployment state of the monitor pod", "enum": ["DEPLOYMENT_STATE_UNSPECIFIED", "NOT_INSTALLED", "INSTALLED", "ERROR", "PENDING"], "enumDescriptions": ["Deployment's state cannot be determined", "Deployment is not installed", "Deployment is installed", "Deployment was attempted to be installed, but has errors", "Deployment is installing or terminating"], "type": "string"}, "otelCollector": {"description": "Deployment state of otel-collector", "enum": ["DEPLOYMENT_STATE_UNSPECIFIED", "NOT_INSTALLED", "INSTALLED", "ERROR", "PENDING"], "enumDescriptions": ["Deployment's state cannot be determined", "Deployment is not installed", "Deployment is installed", "Deployment was attempted to be installed, but has errors", "Deployment is installing or terminating"], "type": "string"}, "reconcilerManager": {"description": "Deployment state of reconciler-manager pod", "enum": ["DEPLOYMENT_STATE_UNSPECIFIED", "NOT_INSTALLED", "INSTALLED", "ERROR", "PENDING"], "enumDescriptions": ["Deployment's state cannot be determined", "Deployment is not installed", "Deployment is installed", "Deployment was attempted to be installed, but has errors", "Deployment is installing or terminating"], "type": "string"}, "resourceGroupControllerManager": {"description": "Deployment state of resource-group-controller-manager", "enum": ["DEPLOYMENT_STATE_UNSPECIFIED", "NOT_INSTALLED", "INSTALLED", "ERROR", "PENDING"], "enumDescriptions": ["Deployment's state cannot be determined", "Deployment is not installed", "Deployment is installed", "Deployment was attempted to be installed, but has errors", "Deployment is installing or terminating"], "type": "string"}, "rootReconciler": {"description": "Deployment state of root-reconciler", "enum": ["DEPLOYMENT_STATE_UNSPECIFIED", "NOT_INSTALLED", "INSTALLED", "ERROR", "PENDING"], "enumDescriptions": ["Deployment's state cannot be determined", "Deployment is not installed", "Deployment is installed", "Deployment was attempted to be installed, but has errors", "Deployment is installing or terminating"], "type": "string"}, "syncer": {"description": "Deployment state of the syncer pod", "enum": ["DEPLOYMENT_STATE_UNSPECIFIED", "NOT_INSTALLED", "INSTALLED", "ERROR", "PENDING"], "enumDescriptions": ["Deployment's state cannot be determined", "Deployment is not installed", "Deployment is installed", "Deployment was attempted to be installed, but has errors", "Deployment is installing or terminating"], "type": "string"}}, "type": "object"}, "ConfigManagementConfigSyncError": {"description": "Errors pertaining to the installation of Config Sync", "id": "ConfigManagementConfigSyncError", "properties": {"errorMessage": {"description": "A string representing the user facing error message", "type": "string"}}, "type": "object"}, "ConfigManagementConfigSyncState": {"description": "State information for ConfigSync", "id": "ConfigManagementConfigSyncState", "properties": {"clusterLevelStopSyncingState": {"description": "Output only. Whether syncing resources to the cluster is stopped at the cluster level.", "enum": ["STOP_SYNCING_STATE_UNSPECIFIED", "NOT_STOPPED", "PENDING", "STOPPED"], "enumDescriptions": ["State cannot be determined", "Syncing resources to the cluster is not stopped at the cluster level.", "Some reconcilers stop syncing resources to the cluster, while others are still syncing.", "Syncing resources to the cluster is stopped at the cluster level."], "readOnly": true, "type": "string"}, "crCount": {"description": "Output only. The number of RootSync and RepoSync CRs in the cluster.", "format": "int32", "readOnly": true, "type": "integer"}, "deploymentState": {"$ref": "ConfigManagementConfigSyncDeploymentState", "description": "Output only. Information about the deployment of ConfigSync, including the version of the various Pods deployed", "readOnly": true}, "errors": {"description": "Output only. Errors pertaining to the installation of Config Sync.", "items": {"$ref": "ConfigManagementConfigSyncError"}, "readOnly": true, "type": "array"}, "reposyncCrd": {"description": "Output only. The state of the Reposync CRD", "enum": ["CRD_STATE_UNSPECIFIED", "NOT_INSTALLED", "INSTALLED", "TERMINATING", "INSTALLING"], "enumDescriptions": ["CRD's state cannot be determined", "CRD is not installed", "CRD is installed", "CRD is terminating (i.e., it has been deleted and is cleaning up)", "CRD is installing"], "readOnly": true, "type": "string"}, "rootsyncCrd": {"description": "Output only. The state of the RootSync CRD", "enum": ["CRD_STATE_UNSPECIFIED", "NOT_INSTALLED", "INSTALLED", "TERMINATING", "INSTALLING"], "enumDescriptions": ["CRD's state cannot be determined", "CRD is not installed", "CRD is installed", "CRD is terminating (i.e., it has been deleted and is cleaning up)", "CRD is installing"], "readOnly": true, "type": "string"}, "state": {"description": "Output only. The state of CS This field summarizes the other fields in this message.", "enum": ["STATE_UNSPECIFIED", "CONFIG_SYNC_NOT_INSTALLED", "CONFIG_SYNC_INSTALLED", "CONFIG_SYNC_ERROR", "CONFIG_SYNC_PENDING"], "enumDescriptions": ["CS's state cannot be determined.", "CS is not installed.", "The expected CS version is installed successfully.", "CS encounters errors.", "CS is installing or terminating."], "readOnly": true, "type": "string"}, "syncState": {"$ref": "ConfigManagementSyncState", "description": "Output only. The state of ConfigSync's process to sync configs to a cluster", "readOnly": true}, "version": {"$ref": "ConfigManagementConfigSyncVersion", "description": "Output only. The version of ConfigSync deployed", "readOnly": true}}, "type": "object"}, "ConfigManagementConfigSyncVersion": {"description": "Specific versioning information pertaining to ConfigSync's Pods", "id": "ConfigManagementConfigSyncVersion", "properties": {"admissionWebhook": {"description": "Version of the deployed admission-webhook pod", "type": "string"}, "gitSync": {"description": "Version of the deployed git-sync pod", "type": "string"}, "importer": {"description": "Version of the deployed importer pod", "type": "string"}, "monitor": {"description": "Version of the deployed monitor pod", "type": "string"}, "otelCollector": {"description": "Version of the deployed otel-collector pod", "type": "string"}, "reconcilerManager": {"description": "Version of the deployed reconciler-manager pod", "type": "string"}, "resourceGroupControllerManager": {"description": "Version of the deployed resource-group-controller-manager pod", "type": "string"}, "rootReconciler": {"description": "Version of the deployed reconciler container in root-reconciler pod", "type": "string"}, "syncer": {"description": "Version of the deployed syncer pod", "type": "string"}}, "type": "object"}, "ConfigManagementContainerOverride": {"description": "Configuration for a container override.", "id": "ConfigManagementContainerOverride", "properties": {"containerName": {"description": "Required. The name of the container.", "type": "string"}, "cpuLimit": {"description": "Optional. The cpu limit of the container.", "type": "string"}, "cpuRequest": {"description": "Optional. The cpu request of the container.", "type": "string"}, "memoryLimit": {"description": "Optional. The memory limit of the container.", "type": "string"}, "memoryRequest": {"description": "Optional. The memory request of the container.", "type": "string"}}, "type": "object"}, "ConfigManagementDeploymentOverride": {"description": "Configuration for a deployment override.", "id": "ConfigManagementDeploymentOverride", "properties": {"containers": {"description": "Optional. The containers of the deployment resource to be overridden.", "items": {"$ref": "ConfigManagementContainerOverride"}, "type": "array"}, "deploymentName": {"description": "Required. The name of the deployment resource to be overridden.", "type": "string"}, "deploymentNamespace": {"description": "Required. The namespace of the deployment resource to be overridden..", "type": "string"}}, "type": "object"}, "ConfigManagementErrorResource": {"description": "Model for a config file in the git repo with an associated Sync error", "id": "ConfigManagementErrorResource", "properties": {"resourceGvk": {"$ref": "ConfigManagementGroupVersionKind", "description": "Group/version/kind of the resource that is causing an error"}, "resourceName": {"description": "Metadata name of the resource that is causing an error", "type": "string"}, "resourceNamespace": {"description": "Namespace of the resource that is causing an error", "type": "string"}, "sourcePath": {"description": "Path in the git repo of the erroneous config", "type": "string"}}, "type": "object"}, "ConfigManagementGatekeeperDeploymentState": {"description": "State of Policy Controller installation.", "id": "ConfigManagementGatekeeperDeploymentState", "properties": {"gatekeeperAudit": {"description": "Status of gatekeeper-audit deployment.", "enum": ["DEPLOYMENT_STATE_UNSPECIFIED", "NOT_INSTALLED", "INSTALLED", "ERROR", "PENDING"], "enumDescriptions": ["Deployment's state cannot be determined", "Deployment is not installed", "Deployment is installed", "Deployment was attempted to be installed, but has errors", "Deployment is installing or terminating"], "type": "string"}, "gatekeeperControllerManagerState": {"description": "Status of gatekeeper-controller-manager pod.", "enum": ["DEPLOYMENT_STATE_UNSPECIFIED", "NOT_INSTALLED", "INSTALLED", "ERROR", "PENDING"], "enumDescriptions": ["Deployment's state cannot be determined", "Deployment is not installed", "Deployment is installed", "Deployment was attempted to be installed, but has errors", "Deployment is installing or terminating"], "type": "string"}, "gatekeeperMutation": {"description": "Status of the pod serving the mutation webhook.", "enum": ["DEPLOYMENT_STATE_UNSPECIFIED", "NOT_INSTALLED", "INSTALLED", "ERROR", "PENDING"], "enumDescriptions": ["Deployment's state cannot be determined", "Deployment is not installed", "Deployment is installed", "Deployment was attempted to be installed, but has errors", "Deployment is installing or terminating"], "type": "string"}}, "type": "object"}, "ConfigManagementGitConfig": {"description": "Git repo configuration for a single cluster.", "id": "ConfigManagementGitConfig", "properties": {"gcpServiceAccountEmail": {"description": "Optional. The Google Cloud Service Account Email used for auth when secret_type is gcpServiceAccount.", "type": "string"}, "httpsProxy": {"description": "Optional. URL for the HTTPS proxy to be used when communicating with the Git repo.", "type": "string"}, "policyDir": {"description": "Optional. The path within the Git repository that represents the top level of the repo to sync. Default: the root directory of the repository.", "type": "string"}, "secretType": {"description": "Required. Type of secret configured for access to the Git repo. Must be one of ssh, cookiefile, gcenode, token, gcpserviceaccount, githubapp or none. The validation of this is case-sensitive.", "type": "string"}, "syncBranch": {"description": "Optional. The branch of the repository to sync from. <PERSON><PERSON><PERSON>: master.", "type": "string"}, "syncRepo": {"description": "Required. The URL of the Git repository to use as the source of truth.", "type": "string"}, "syncRev": {"description": "Optional. Git revision (tag or hash) to check out. Default HEAD.", "type": "string"}, "syncWaitSecs": {"description": "Optional. Period in seconds between consecutive syncs. De<PERSON>ult: 15.", "format": "int64", "type": "string"}}, "type": "object"}, "ConfigManagementGroupVersionKind": {"description": "A Kubernetes object's GVK", "id": "ConfigManagementGroupVersionKind", "properties": {"group": {"description": "Kubernetes Group", "type": "string"}, "kind": {"description": "Kubernetes Kind", "type": "string"}, "version": {"description": "Kubernetes Version", "type": "string"}}, "type": "object"}, "ConfigManagementHierarchyControllerConfig": {"description": "Configuration for Hierarchy Controller", "id": "ConfigManagementHierarchyControllerConfig", "properties": {"enableHierarchicalResourceQuota": {"description": "Whether hierarchical resource quota is enabled in this cluster.", "type": "boolean"}, "enablePodTreeLabels": {"description": "Whether pod tree labels are enabled in this cluster.", "type": "boolean"}, "enabled": {"description": "Whether Hierarchy Controller is enabled in this cluster.", "type": "boolean"}}, "type": "object"}, "ConfigManagementHierarchyControllerDeploymentState": {"description": "Deployment state for Hierarchy Controller", "id": "ConfigManagementHierarchyControllerDeploymentState", "properties": {"extension": {"description": "The deployment state for Hierarchy Controller extension (e.g. v0.7.0-hc.1)", "enum": ["DEPLOYMENT_STATE_UNSPECIFIED", "NOT_INSTALLED", "INSTALLED", "ERROR", "PENDING"], "enumDescriptions": ["Deployment's state cannot be determined", "Deployment is not installed", "Deployment is installed", "Deployment was attempted to be installed, but has errors", "Deployment is installing or terminating"], "type": "string"}, "hnc": {"description": "The deployment state for open source HNC (e.g. v0.7.0-hc.0)", "enum": ["DEPLOYMENT_STATE_UNSPECIFIED", "NOT_INSTALLED", "INSTALLED", "ERROR", "PENDING"], "enumDescriptions": ["Deployment's state cannot be determined", "Deployment is not installed", "Deployment is installed", "Deployment was attempted to be installed, but has errors", "Deployment is installing or terminating"], "type": "string"}}, "type": "object"}, "ConfigManagementHierarchyControllerState": {"description": "State for Hierarchy Controller", "id": "ConfigManagementHierarchyControllerState", "properties": {"state": {"$ref": "ConfigManagementHierarchyControllerDeploymentState", "description": "The deployment state for Hierarchy Controller"}, "version": {"$ref": "ConfigManagementHierarchyControllerVersion", "description": "The version for Hierarchy Controller"}}, "type": "object"}, "ConfigManagementHierarchyControllerVersion": {"description": "Version for Hierarchy Controller", "id": "ConfigManagementHierarchyControllerVersion", "properties": {"extension": {"description": "Version for Hierarchy Controller extension", "type": "string"}, "hnc": {"description": "Version for open source HNC", "type": "string"}}, "type": "object"}, "ConfigManagementInstallError": {"description": "Errors pertaining to the installation of ACM", "id": "ConfigManagementInstallError", "properties": {"errorMessage": {"description": "A string representing the user facing error message", "type": "string"}}, "type": "object"}, "ConfigManagementMembershipSpec": {"description": "**Anthos Config Management**: Configuration for a single cluster. Intended to parallel the ConfigManagement CR.", "id": "ConfigManagementMembershipSpec", "properties": {"cluster": {"description": "Optional. The user-specified cluster name used by Config Sync cluster-name-selector annotation or ClusterSelector, for applying configs to only a subset of clusters. Omit this field if the cluster's fleet membership name is used by Config Sync cluster-name-selector annotation or ClusterSelector. Set this field if a name different from the cluster's fleet membership name is used by Config Sync cluster-name-selector annotation or ClusterSelector.", "type": "string"}, "configSync": {"$ref": "ConfigManagementConfigSync", "description": "Optional. Config Sync configuration for the cluster."}, "hierarchyController": {"$ref": "ConfigManagementHierarchyControllerConfig", "deprecated": true, "description": "Optional. Hierarchy Controller configuration for the cluster. Deprecated: Configuring Hierarchy Controller through the configmanagement feature is no longer recommended. Use https://github.com/kubernetes-sigs/hierarchical-namespaces instead."}, "management": {"description": "Optional. Enables automatic Feature management.", "enum": ["MANAGEMENT_UNSPECIFIED", "MANAGEMENT_AUTOMATIC", "MANAGEMENT_MANUAL"], "enumDescriptions": ["Unspecified", "Google will manage the Feature for the cluster.", "User will manually manage the Feature for the cluster."], "type": "string"}, "policyController": {"$ref": "ConfigManagementPolicyController", "deprecated": true, "description": "Optional. Policy Controller configuration for the cluster. Deprecated: Configuring Policy Controller through the configmanagement feature is no longer recommended. Use the policycontroller feature instead."}, "version": {"description": "Optional. Version of ACM installed.", "type": "string"}}, "type": "object"}, "ConfigManagementMembershipState": {"description": "**Anthos Config Management**: State for a single cluster.", "id": "ConfigManagementMembershipState", "properties": {"clusterName": {"description": "Output only. This field is set to the `cluster_name` field of the Membership Spec if it is not empty. Otherwise, it is set to the cluster's fleet membership name.", "readOnly": true, "type": "string"}, "configSyncState": {"$ref": "ConfigManagementConfigSyncState", "description": "Output only. Current sync status", "readOnly": true}, "hierarchyControllerState": {"$ref": "ConfigManagementHierarchyControllerState", "description": "Output only. Hierarchy Controller status", "readOnly": true}, "membershipSpec": {"$ref": "ConfigManagementMembershipSpec", "description": "Output only. Membership configuration in the cluster. This represents the actual state in the cluster, while the MembershipSpec in the FeatureSpec represents the intended state", "readOnly": true}, "operatorState": {"$ref": "ConfigManagementOperatorState", "description": "Output only. Current install status of ACM's Operator", "readOnly": true}, "policyControllerState": {"$ref": "ConfigManagementPolicyControllerState", "description": "Output only. PolicyController status", "readOnly": true}}, "type": "object"}, "ConfigManagementOciConfig": {"description": "OCI repo configuration for a single cluster", "id": "ConfigManagementOciConfig", "properties": {"gcpServiceAccountEmail": {"description": "Optional. The Google Cloud Service Account Email used for auth when secret_type is gcpServiceAccount.", "type": "string"}, "policyDir": {"description": "Optional. The absolute path of the directory that contains the local resources. Default: the root directory of the image.", "type": "string"}, "secretType": {"description": "Required. Type of secret configured for access to the OCI repo. Must be one of gcenode, gcpserviceaccount, k8sserviceaccount or none. The validation of this is case-sensitive.", "type": "string"}, "syncRepo": {"description": "Required. The OCI image repository URL for the package to sync from. e.g. `LOCATION-docker.pkg.dev/PROJECT_ID/REPOSITORY_NAME/PACKAGE_NAME`.", "type": "string"}, "syncWaitSecs": {"description": "Optional. Period in seconds between consecutive syncs. De<PERSON>ult: 15.", "format": "int64", "type": "string"}}, "type": "object"}, "ConfigManagementOperatorState": {"description": "State information for an ACM's Operator", "id": "ConfigManagementOperatorState", "properties": {"deploymentState": {"description": "The state of the Operator's deployment", "enum": ["DEPLOYMENT_STATE_UNSPECIFIED", "NOT_INSTALLED", "INSTALLED", "ERROR", "PENDING"], "enumDescriptions": ["Deployment's state cannot be determined", "Deployment is not installed", "Deployment is installed", "Deployment was attempted to be installed, but has errors", "Deployment is installing or terminating"], "type": "string"}, "errors": {"description": "Install errors.", "items": {"$ref": "ConfigManagementInstallError"}, "type": "array"}, "version": {"description": "The semenatic version number of the operator", "type": "string"}}, "type": "object"}, "ConfigManagementPolicyController": {"description": "Configuration for Policy Controller", "id": "ConfigManagementPolicyController", "properties": {"auditIntervalSeconds": {"description": "Sets the interval for Policy Controller <PERSON><PERSON> (in seconds). When set to 0, this disables audit functionality altogether.", "format": "int64", "type": "string"}, "enabled": {"description": "Enables the installation of Policy Controller. If false, the rest of PolicyController fields take no effect.", "type": "boolean"}, "exemptableNamespaces": {"description": "The set of namespaces that are excluded from Policy Controller checks. Namespaces do not need to currently exist on the cluster.", "items": {"type": "string"}, "type": "array"}, "logDeniesEnabled": {"description": "Logs all denies and dry run failures.", "type": "boolean"}, "monitoring": {"$ref": "ConfigManagementPolicyControllerMonitoring", "description": "Monitoring specifies the configuration of monitoring."}, "mutationEnabled": {"description": "Enable or disable mutation in policy controller. If true, mutation CRDs, webhook and controller deployment will be deployed to the cluster.", "type": "boolean"}, "referentialRulesEnabled": {"description": "Enables the ability to use Constraint Templates that reference to objects other than the object currently being evaluated.", "type": "boolean"}, "templateLibraryInstalled": {"description": "Installs the default template library along with Policy Controller.", "type": "boolean"}, "updateTime": {"description": "Output only. Last time this membership spec was updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "ConfigManagementPolicyControllerMigration": {"description": "State for the migration of PolicyController from ACM -> PoCo Hub.", "id": "ConfigManagementPolicyControllerMigration", "properties": {"copyTime": {"description": "Last time this membership spec was copied to PoCo feature.", "format": "google-datetime", "type": "string"}, "stage": {"description": "Stage of the migration.", "enum": ["STAGE_UNSPECIFIED", "ACM_MANAGED", "POCO_MANAGED"], "enumDescriptions": ["Unknown state of migration.", "ACM Hub/Operator manages policycontroller. No migration yet completed.", "All migrations steps complete; Poco Hub now manages policycontroller."], "type": "string"}}, "type": "object"}, "ConfigManagementPolicyControllerMonitoring": {"description": "PolicyControllerMonitoring specifies the backends Policy Controller should export metrics to. For example, to specify metrics should be exported to Cloud Monitoring and Prometheus, specify backends: [\"cloudmonitoring\", \"prometheus\"]", "id": "ConfigManagementPolicyControllerMonitoring", "properties": {"backends": {"description": "Specifies the list of backends Policy Controller will export to. An empty list would effectively disable metrics export.", "items": {"enum": ["MONITORING_BACKEND_UNSPECIFIED", "PROMETHEUS", "CLOUD_MONITORING"], "enumDescriptions": ["Backend cannot be determined", "Prometheus backend for monitoring", "Stackdriver/Cloud Monitoring backend for monitoring"], "type": "string"}, "type": "array"}}, "type": "object"}, "ConfigManagementPolicyControllerState": {"description": "State for PolicyControllerState.", "id": "ConfigManagementPolicyControllerState", "properties": {"deploymentState": {"$ref": "ConfigManagementGatekeeperDeploymentState", "description": "The state about the policy controller installation."}, "migration": {"$ref": "ConfigManagementPolicyControllerMigration", "description": "Record state of ACM -> PoCo Hub migration for this feature."}, "version": {"$ref": "ConfigManagementPolicyControllerVersion", "description": "The version of Gatekeeper Policy Controller deployed."}}, "type": "object"}, "ConfigManagementPolicyControllerVersion": {"description": "The build version of Gatekeeper Policy Controller is using.", "id": "ConfigManagementPolicyControllerVersion", "properties": {"version": {"description": "The gatekeeper image tag that is composed of ACM version, git tag, build number.", "type": "string"}}, "type": "object"}, "ConfigManagementSyncError": {"description": "An ACM created error representing a problem syncing configurations", "id": "ConfigManagementSyncError", "properties": {"code": {"description": "An ACM defined error code", "type": "string"}, "errorMessage": {"description": "A description of the error", "type": "string"}, "errorResources": {"description": "A list of config(s) associated with the error, if any", "items": {"$ref": "ConfigManagementErrorResource"}, "type": "array"}}, "type": "object"}, "ConfigManagementSyncState": {"description": "State indicating an ACM's progress syncing configurations to a cluster", "id": "ConfigManagementSyncState", "properties": {"code": {"description": "Sync status code", "enum": ["SYNC_CODE_UNSPECIFIED", "SYNCED", "PENDING", "ERROR", "NOT_CONFIGURED", "NOT_INSTALLED", "UNAUTHORIZED", "UNREACHABLE"], "enumDescriptions": ["Config Sync cannot determine a sync code", "Config Sync successfully synced the git Repo with the cluster", "Config Sync is in the progress of syncing a new change", "Indicates an error configuring Config Sync, and user action is required", "Config Sync has been installed but not configured", "Config Sync has not been installed", "Error authorizing with the cluster", "Cluster could not be reached"], "type": "string"}, "errors": {"description": "A list of errors resulting from problematic configs. This list will be truncated after 100 errors, although it is unlikely for that many errors to simultaneously exist.", "items": {"$ref": "ConfigManagementSyncError"}, "type": "array"}, "importToken": {"description": "Token indicating the state of the importer.", "type": "string"}, "lastSync": {"deprecated": true, "description": "Deprecated: use last_sync_time instead. Timestamp of when ACM last successfully synced the repo The time format is specified in https://golang.org/pkg/time/#Time.String", "type": "string"}, "lastSyncTime": {"description": "Timestamp type of when ACM last successfully synced the repo", "format": "google-datetime", "type": "string"}, "sourceToken": {"description": "Token indicating the state of the repo.", "type": "string"}, "syncToken": {"description": "Token indicating the state of the syncer.", "type": "string"}}, "type": "object"}, "ConnectAgentResource": {"description": "ConnectAgentResource represents a Kubernetes resource manifest for Connect Agent deployment.", "id": "ConnectAgentResource", "properties": {"manifest": {"description": "YAML manifest of the resource.", "type": "string"}, "type": {"$ref": "TypeMeta", "description": "Kubernetes type of the resource."}}, "type": "object"}, "DataplaneV2FeatureSpec": {"description": "**Dataplane V2**: Spec", "id": "DataplaneV2FeatureSpec", "properties": {"enableEncryption": {"description": "Enable dataplane-v2 based encryption for multiple clusters.", "type": "boolean"}}, "type": "object"}, "DefaultClusterConfig": {"description": "DefaultClusterConfig describes the default cluster configurations to be applied to all clusters born-in-fleet.", "id": "DefaultClusterConfig", "properties": {"binaryAuthorizationConfig": {"$ref": "BinaryAuthorizationConfig", "description": "Optional. Enable/Disable binary authorization features for the cluster."}, "compliancePostureConfig": {"$ref": "CompliancePostureConfig", "description": "Optional. Enable/Disable Compliance Posture features for the cluster. Note that on UpdateFleet, only full replacement of this field is allowed. Users are not allowed for partial updates through field mask."}, "securityPostureConfig": {"$ref": "SecurityPostureConfig", "description": "Enable/Disable Security Posture features for the cluster."}}, "type": "object"}, "EdgeCluster": {"description": "EdgeCluster contains information specific to Google Edge Clusters.", "id": "EdgeCluster", "properties": {"resourceLink": {"description": "Immutable. Self-link of the Google Cloud resource for the Edge Cluster. For example: //edgecontainer.googleapis.com/projects/my-project/locations/us-west1-a/clusters/my-cluster", "type": "string"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "Expr": {"description": "Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: \"Summary size limit\" description: \"Determines if a summary is less than 100 chars\" expression: \"document.summary.size() < 100\" Example (Equality): title: \"Requestor is owner\" description: \"Determines if requestor is the document owner\" expression: \"document.owner == request.auth.claims.email\" Example (Logic): title: \"Public documents\" description: \"Determine whether the document should be publicly visible\" expression: \"document.type != 'private' && document.type != 'internal'\" Example (Data Manipulation): title: \"Notification string\" description: \"Create a notification string with a timestamp.\" expression: \"'New message received at ' + string(document.create_time)\" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.", "id": "Expr", "properties": {"description": {"description": "Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.", "type": "string"}, "expression": {"description": "Textual representation of an expression in Common Expression Language syntax.", "type": "string"}, "location": {"description": "Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.", "type": "string"}, "title": {"description": "Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.", "type": "string"}}, "type": "object"}, "Feature": {"description": "Feature represents the settings and status of any Fleet Feature.", "id": "Feature", "properties": {"createTime": {"description": "Output only. When the Feature resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "deleteTime": {"description": "Output only. When the Feature resource was deleted.", "format": "google-datetime", "readOnly": true, "type": "string"}, "fleetDefaultMemberConfig": {"$ref": "CommonFleetDefaultMemberConfigSpec", "description": "Optional. Feature configuration applicable to all memberships of the fleet."}, "labels": {"additionalProperties": {"type": "string"}, "description": "Labels for this Feature.", "type": "object"}, "membershipSpecs": {"additionalProperties": {"$ref": "MembershipFeatureSpec"}, "description": "Optional. Membership-specific configuration for this Feature. If this Feature does not support any per-Membership configuration, this field may be unused. The keys indicate which Membership the configuration is for, in the form: `projects/{p}/locations/{l}/memberships/{m}` Where {p} is the project, {l} is a valid location and {m} is a valid Membership in this project at that location. {p} WILL match the Feature's project. {p} will always be returned as the project number, but the project ID is also accepted during input. If the same Membership is specified in the map twice (using the project ID form, and the project number form), exactly ONE of the entries will be saved, with no guarantees as to which. For this reason, it is recommended the same format be used for all entries when mutating a Feature.", "type": "object"}, "membershipStates": {"additionalProperties": {"$ref": "MembershipFeatureState"}, "description": "Output only. Membership-specific Feature status. If this Feature does report any per-Membership status, this field may be unused. The keys indicate which Membership the state is for, in the form: `projects/{p}/locations/{l}/memberships/{m}` Where {p} is the project number, {l} is a valid location and {m} is a valid Membership in this project at that location. {p} MUST match the Feature's project number.", "readOnly": true, "type": "object"}, "name": {"description": "Output only. The full, unique name of this Feature resource in the format `projects/*/locations/*/features/*`.", "readOnly": true, "type": "string"}, "resourceState": {"$ref": "FeatureResourceState", "description": "Output only. State of the Feature resource itself.", "readOnly": true}, "scopeSpecs": {"additionalProperties": {"$ref": "ScopeFeatureSpec"}, "description": "Optional. Scope-specific configuration for this Feature. If this Feature does not support any per-Scope configuration, this field may be unused. The keys indicate which <PERSON><PERSON> the configuration is for, in the form: `projects/{p}/locations/global/scopes/{s}` Where {p} is the project, {s} is a valid Scope in this project. {p} WILL match the Feature's project. {p} will always be returned as the project number, but the project ID is also accepted during input. If the same <PERSON>ope is specified in the map twice (using the project ID form, and the project number form), exactly ONE of the entries will be saved, with no guarantees as to which. For this reason, it is recommended the same format be used for all entries when mutating a Feature.", "type": "object"}, "scopeStates": {"additionalProperties": {"$ref": "ScopeFeatureState"}, "description": "Output only. Scope-specific Feature status. If this Feature does report any per-Scope status, this field may be unused. The keys indicate which Scope the state is for, in the form: `projects/{p}/locations/global/scopes/{s}` Where {p} is the project, {s} is a valid Scope in this project. {p} WILL match the Feature's project.", "readOnly": true, "type": "object"}, "spec": {"$ref": "CommonFeatureSpec", "description": "Optional. Fleet-wide Feature configuration. If this Feature does not support any Fleet-wide configuration, this field may be unused."}, "state": {"$ref": "CommonFeatureState", "description": "Output only. The Fleet-wide Feature state.", "readOnly": true}, "unreachable": {"description": "Output only. List of locations that could not be reached while fetching this feature.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "updateTime": {"description": "Output only. When the Feature resource was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "FeatureResourceState": {"description": "FeatureResourceState describes the state of a Feature *resource* in the GkeHub API. See `FeatureState` for the \"running state\" of the Feature in the Fleet and across Memberships.", "id": "FeatureResourceState", "properties": {"state": {"description": "The current state of the Feature resource in the Hub API.", "enum": ["STATE_UNSPECIFIED", "ENABLING", "ACTIVE", "DISABLING", "UPDATING", "SERVICE_UPDATING"], "enumDescriptions": ["State is unknown or not set.", "The Feature is being enabled, and the Feature resource is being created. Once complete, the corresponding Feature will be enabled in this Fleet.", "The Feature is enabled in this Fleet, and the Feature resource is fully available.", "The Feature is being disabled in this Fleet, and the Feature resource is being deleted.", "The Feature resource is being updated.", "The Feature resource is being updated by the Hub Service."], "type": "string"}}, "type": "object"}, "FeatureState": {"description": "FeatureState describes the high-level state of a Feature. It may be used to describe a Feature's state at the environ-level, or per-membershop, depending on the context.", "id": "FeatureState", "properties": {"code": {"description": "The high-level, machine-readable status of this Feature.", "enum": ["CODE_UNSPECIFIED", "OK", "WARNING", "ERROR"], "enumDescriptions": ["Unknown or not set.", "The Feature is operating normally.", "The Feature has encountered an issue, and is operating in a degraded state. The Feature may need intervention to return to normal operation. See the description and any associated Feature-specific details for more information.", "The Feature is not operating or is in a severely degraded state. The Feature may need intervention to return to normal operation. See the description and any associated Feature-specific details for more information."], "type": "string"}, "description": {"description": "A human-readable description of the current status.", "type": "string"}, "updateTime": {"description": "The time this status and any related Feature-specific details were updated.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "Fleet": {"description": "Fleet contains the Fleet-wide metadata and configuration.", "id": "Fleet", "properties": {"createTime": {"description": "Output only. When the Fleet was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "defaultClusterConfig": {"$ref": "DefaultClusterConfig", "description": "Optional. The default cluster configurations to apply across the fleet."}, "deleteTime": {"description": "Output only. When the Fleet was deleted.", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "Optional. A user-assigned display name of the Fleet. When present, it must be between 4 to 30 characters. Allowed characters are: lowercase and uppercase letters, numbers, hyphen, single-quote, double-quote, space, and exclamation point. Example: `Production Fleet`", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Labels for this Fleet.", "type": "object"}, "name": {"description": "Output only. The full, unique resource name of this fleet in the format of `projects/{project}/locations/{location}/fleets/{fleet}`. Each Google Cloud project can have at most one fleet resource, named \"default\".", "readOnly": true, "type": "string"}, "state": {"$ref": "FleetLifecycleState", "description": "Output only. State of the namespace resource.", "readOnly": true}, "uid": {"description": "Output only. Google-generated UUID for this resource. This is unique across all Fleet resources. If a Fleet resource is deleted and another resource with the same name is created, it gets a different uid.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. When the Fleet was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "FleetLifecycleState": {"description": "FleetLifecycleState describes the state of a Fleet resource.", "id": "FleetLifecycleState", "properties": {"code": {"description": "Output only. The current state of the Fleet resource.", "enum": ["CODE_UNSPECIFIED", "CREATING", "READY", "DELETING", "UPDATING"], "enumDescriptions": ["The code is not set.", "The fleet is being created.", "The fleet active.", "The fleet is being deleted.", "The fleet is being updated."], "readOnly": true, "type": "string"}}, "type": "object"}, "FleetObservabilityFeatureError": {"description": "All error details of the fleet observability feature.", "id": "FleetObservabilityFeatureError", "properties": {"code": {"description": "The code of the error.", "type": "string"}, "description": {"description": "A human-readable description of the current status.", "type": "string"}}, "type": "object"}, "FleetObservabilityFeatureSpec": {"description": "**Fleet Observability**: The Hub-wide input for the FleetObservability feature.", "id": "FleetObservabilityFeatureSpec", "properties": {"loggingConfig": {"$ref": "FleetObservabilityLoggingConfig", "description": "Specified if fleet logging feature is enabled for the entire fleet. If UNSPECIFIED, fleet logging feature is disabled for the entire fleet."}}, "type": "object"}, "FleetObservabilityFeatureState": {"description": "**FleetObservability**: Hub-wide Feature for FleetObservability feature. state.", "id": "FleetObservabilityFeatureState", "properties": {"logging": {"$ref": "FleetObservabilityFleetObservabilityLoggingState", "description": "The feature state of default logging."}, "monitoring": {"$ref": "FleetObservabilityFleetObservabilityMonitoringState", "description": "The feature state of fleet monitoring."}}, "type": "object"}, "FleetObservabilityFleetObservabilityBaseFeatureState": {"description": "Base state for fleet observability feature.", "id": "FleetObservabilityFleetObservabilityBaseFeatureState", "properties": {"code": {"description": "The high-level, machine-readable status of this Feature.", "enum": ["CODE_UNSPECIFIED", "OK", "ERROR"], "enumDescriptions": ["Unknown or not set.", "The Feature is operating normally.", "The Feature is encountering errors in the reconciliation. The Feature may need intervention to return to normal operation. See the description and any associated Feature-specific details for more information."], "type": "string"}, "errors": {"description": "Errors after reconciling the monitoring and logging feature if the code is not OK.", "items": {"$ref": "FleetObservabilityFeatureError"}, "type": "array"}}, "type": "object"}, "FleetObservabilityFleetObservabilityLoggingState": {"description": "Feature state for logging feature.", "id": "FleetObservabilityFleetObservabilityLoggingState", "properties": {"defaultLog": {"$ref": "FleetObservabilityFleetObservabilityBaseFeatureState", "description": "The base feature state of fleet default log."}, "scopeLog": {"$ref": "FleetObservabilityFleetObservabilityBaseFeatureState", "description": "The base feature state of fleet scope log."}}, "type": "object"}, "FleetObservabilityFleetObservabilityMonitoringState": {"description": "Feature state for monitoring feature.", "id": "FleetObservabilityFleetObservabilityMonitoringState", "properties": {"state": {"$ref": "FleetObservabilityFleetObservabilityBaseFeatureState", "description": "The base feature state of fleet monitoring feature."}}, "type": "object"}, "FleetObservabilityLoggingConfig": {"description": "LoggingConfig defines the configuration for different types of logs.", "id": "FleetObservabilityLoggingConfig", "properties": {"defaultConfig": {"$ref": "FleetObservabilityRoutingConfig", "description": "Specified if applying the default routing config to logs not specified in other configs."}, "fleetScopeLogsConfig": {"$ref": "FleetObservabilityRoutingConfig", "description": "Specified if applying the routing config to all logs for all fleet scopes."}}, "type": "object"}, "FleetObservabilityMembershipSpec": {"description": "**FleetObservability**: The membership-specific input for FleetObservability feature.", "id": "FleetObservabilityMembershipSpec", "properties": {}, "type": "object"}, "FleetObservabilityMembershipState": {"description": "**FleetObservability**: Membership-specific Feature state for fleetobservability.", "id": "FleetObservabilityMembershipState", "properties": {}, "type": "object"}, "FleetObservabilityRoutingConfig": {"description": "RoutingConfig configures the behaviour of fleet logging feature.", "id": "FleetObservabilityRoutingConfig", "properties": {"mode": {"description": "mode configures the logs routing mode.", "enum": ["MODE_UNSPECIFIED", "COPY", "MOVE"], "enumDescriptions": ["If UNSPECIFIED, fleet logging feature is disabled.", "logs will be copied to the destination project.", "logs will be moved to the destination project."], "type": "string"}}, "type": "object"}, "GenerateConnectManifestResponse": {"description": "GenerateConnectManifestResponse contains manifest information for installing/upgrading a Connect agent.", "id": "GenerateConnectManifestResponse", "properties": {"manifest": {"description": "The ordered list of Kubernetes resources that need to be applied to the cluster for GKE Connect agent installation/upgrade.", "items": {"$ref": "ConnectAgentResource"}, "type": "array"}}, "type": "object"}, "GenerateMembershipRBACRoleBindingYAMLResponse": {"description": "Response for GenerateRBACRoleBindingYAML.", "id": "GenerateMembershipRBACRoleBindingYAMLResponse", "properties": {"roleBindingsYaml": {"description": "a yaml text blob including the RBAC policies.", "type": "string"}}, "type": "object"}, "GkeCluster": {"description": "GkeCluster contains information specific to GKE clusters.", "id": "GkeCluster", "properties": {"clusterMissing": {"description": "Output only. If cluster_missing is set then it denotes that the GKE cluster no longer exists in the GKE Control Plane.", "readOnly": true, "type": "boolean"}, "resourceLink": {"description": "Immutable. Self-link of the Google Cloud resource for the GKE cluster. For example: //container.googleapis.com/projects/my-project/locations/us-west1-a/clusters/my-cluster Zonal clusters are also supported.", "type": "string"}}, "type": "object"}, "GoogleRpcStatus": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "GoogleRpcStatus", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "IdentityServiceAuthMethod": {"description": "Configuration of an auth method for a member/cluster. Only one authentication method (e.g., OIDC and LDAP) can be set per AuthMethod.", "id": "IdentityServiceAuthMethod", "properties": {"azureadConfig": {"$ref": "IdentityServiceAzureADConfig", "description": "AzureAD specific Configuration."}, "googleConfig": {"$ref": "IdentityServiceGoogleConfig", "description": "GoogleConfig specific configuration."}, "ldapConfig": {"$ref": "IdentityServiceLdapConfig", "description": "LDAP specific configuration."}, "name": {"description": "Identifier for auth config.", "type": "string"}, "oidcConfig": {"$ref": "IdentityServiceOidcConfig", "description": "OIDC specific configuration."}, "proxy": {"description": "Proxy server address to use for auth method.", "type": "string"}, "samlConfig": {"$ref": "IdentityServiceSamlConfig", "description": "SAML specific configuration."}}, "type": "object"}, "IdentityServiceAzureADConfig": {"description": "Configuration for the AzureAD Auth flow.", "id": "IdentityServiceAzureADConfig", "properties": {"clientId": {"description": "ID for the registered client application that makes authentication requests to the Azure AD identity provider.", "type": "string"}, "clientSecret": {"description": "Input only. Unencrypted AzureAD client secret will be passed to the GKE Hub CLH.", "type": "string"}, "encryptedClientSecret": {"description": "Output only. Encrypted AzureAD client secret.", "format": "byte", "readOnly": true, "type": "string"}, "groupFormat": {"description": "Optional. Format of the AzureAD groups that the client wants for auth.", "type": "string"}, "kubectlRedirectUri": {"description": "The redirect URL that kubectl uses for authorization.", "type": "string"}, "tenant": {"description": "Kind of Azure AD account to be authenticated. Supported values are or for accounts belonging to a specific tenant.", "type": "string"}, "userClaim": {"description": "Optional. Claim in the AzureAD ID Token that holds the user details.", "type": "string"}}, "type": "object"}, "IdentityServiceDiagnosticInterface": {"description": "Configuration options for the AIS diagnostic interface.", "id": "IdentityServiceDiagnosticInterface", "properties": {"enabled": {"description": "Determines whether to enable the diagnostic interface.", "type": "boolean"}, "expirationTime": {"description": "Determines the expiration time of the diagnostic interface enablement. When reached, requests to the interface would be automatically rejected.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "IdentityServiceGoogleConfig": {"description": "Configuration for the Google Plugin Auth flow.", "id": "IdentityServiceGoogleConfig", "properties": {"disable": {"description": "Disable automatic configuration of Google Plugin on supported platforms.", "type": "boolean"}}, "type": "object"}, "IdentityServiceGroupConfig": {"description": "Contains the properties for locating and authenticating groups in the directory.", "id": "IdentityServiceGroupConfig", "properties": {"baseDn": {"description": "Required. The location of the subtree in the LDAP directory to search for group entries.", "type": "string"}, "filter": {"description": "Optional. Optional filter to be used when searching for groups a user belongs to. This can be used to explicitly match only certain groups in order to reduce the amount of groups returned for each user. This defaults to \"(objectClass=Group)\".", "type": "string"}, "idAttribute": {"description": "Optional. The identifying name of each group a user belongs to. For example, if this is set to \"distinguishedName\" then RBACs and other group expectations should be written as full DNs. This defaults to \"distinguishedName\".", "type": "string"}}, "type": "object"}, "IdentityServiceIdentityServiceOptions": {"description": "Holds non-protocol-related configuration options.", "id": "IdentityServiceIdentityServiceOptions", "properties": {"diagnosticInterface": {"$ref": "IdentityServiceDiagnosticInterface", "description": "Configuration options for the AIS diagnostic interface."}, "sessionDuration": {"description": "Determines the lifespan of STS tokens issued by Anthos Identity Service.", "format": "google-duration", "type": "string"}}, "type": "object"}, "IdentityServiceLdapConfig": {"description": "Configuration for the LDAP Auth flow.", "id": "IdentityServiceLdapConfig", "properties": {"group": {"$ref": "IdentityServiceGroupConfig", "description": "Optional. Contains the properties for locating and authenticating groups in the directory."}, "server": {"$ref": "IdentityServiceServerConfig", "description": "Required. Server settings for the external LDAP server."}, "serviceAccount": {"$ref": "IdentityServiceServiceAccountConfig", "description": "Required. Contains the credentials of the service account which is authorized to perform the LDAP search in the directory. The credentials can be supplied by the combination of the DN and password or the client certificate."}, "user": {"$ref": "IdentityServiceUserConfig", "description": "Required. Defines where users exist in the LDAP directory."}}, "type": "object"}, "IdentityServiceMembershipSpec": {"description": "**Anthos Identity Service**: Configuration for a single Membership.", "id": "IdentityServiceMembershipSpec", "properties": {"authMethods": {"description": "A member may support multiple auth methods.", "items": {"$ref": "IdentityServiceAuthMethod"}, "type": "array"}, "identityServiceOptions": {"$ref": "IdentityServiceIdentityServiceOptions", "description": "Optional. non-protocol-related configuration options."}}, "type": "object"}, "IdentityServiceMembershipState": {"description": "**Anthos Identity Service**: State for a single Membership.", "id": "IdentityServiceMembershipState", "properties": {"failureReason": {"description": "The reason of the failure.", "type": "string"}, "installedVersion": {"description": "Installed AIS version. This is the AIS version installed on this member. The values makes sense iff state is OK.", "type": "string"}, "memberConfig": {"$ref": "IdentityServiceMembershipSpec", "description": "Last reconciled membership configuration"}, "state": {"description": "Deployment state on this member", "enum": ["DEPLOYMENT_STATE_UNSPECIFIED", "OK", "ERROR"], "enumDescriptions": ["Unspecified state", "deployment succeeds", "Failure with error."], "type": "string"}}, "type": "object"}, "IdentityServiceOidcConfig": {"description": "Configuration for OIDC Auth flow.", "id": "IdentityServiceOidcConfig", "properties": {"certificateAuthorityData": {"description": "PEM-encoded CA for OIDC provider.", "type": "string"}, "clientId": {"description": "ID for OIDC client application.", "type": "string"}, "clientSecret": {"description": "Input only. Unencrypted OIDC client secret will be passed to the GKE Hub CLH.", "type": "string"}, "deployCloudConsoleProxy": {"description": "Flag to denote if reverse proxy is used to connect to auth provider. This flag should be set to true when provider is not reachable by Google Cloud Console.", "type": "boolean"}, "enableAccessToken": {"description": "Enable access token.", "type": "boolean"}, "encryptedClientSecret": {"description": "Output only. Encrypted OIDC Client secret", "format": "byte", "readOnly": true, "type": "string"}, "extraParams": {"description": "Comma-separated list of key-value pairs.", "type": "string"}, "groupPrefix": {"description": "Prefix to prepend to group name.", "type": "string"}, "groupsClaim": {"description": "Claim in OIDC ID token that holds group information.", "type": "string"}, "issuerUri": {"description": "URI for the OIDC provider. This should point to the level below .well-known/openid-configuration.", "type": "string"}, "kubectlRedirectUri": {"description": "Registered redirect uri to redirect users going through OAuth flow using kubectl plugin.", "type": "string"}, "scopes": {"description": "Comma-separated list of identifiers.", "type": "string"}, "userClaim": {"description": "Claim in OIDC ID token that holds username.", "type": "string"}, "userPrefix": {"description": "Prefix to prepend to user name.", "type": "string"}}, "type": "object"}, "IdentityServiceSamlConfig": {"description": "Configuration for the SAML Auth flow.", "id": "IdentityServiceSamlConfig", "properties": {"attributeMapping": {"additionalProperties": {"type": "string"}, "description": "Optional. The mapping of additional user attributes like nickname, birthday and address etc.. `key` is the name of this additional attribute. `value` is a string presenting as CEL(common expression language, go/cel) used for getting the value from the resources. Take nickname as an example, in this case, `key` is \"attribute.nickname\" and `value` is \"assertion.nickname\".", "type": "object"}, "groupPrefix": {"description": "Optional. Prefix to prepend to group name.", "type": "string"}, "groupsAttribute": {"description": "Optional. The SAML attribute to read groups from. This value is expected to be a string and will be passed along as-is (with the option of being prefixed by the `group_prefix`).", "type": "string"}, "identityProviderCertificates": {"description": "Required. The list of IdP certificates to validate the SAML response against.", "items": {"type": "string"}, "type": "array"}, "identityProviderId": {"description": "Required. The entity ID of the SAML IdP.", "type": "string"}, "identityProviderSsoUri": {"description": "Required. The URI where the SAML IdP exposes the SSO service.", "type": "string"}, "userAttribute": {"description": "Optional. The SAML attribute to read username from. If unspecified, the username will be read from the NameID element of the assertion in SAML response. This value is expected to be a string and will be passed along as-is (with the option of being prefixed by the `user_prefix`).", "type": "string"}, "userPrefix": {"description": "Optional. Prefix to prepend to user name.", "type": "string"}}, "type": "object"}, "IdentityServiceServerConfig": {"description": "Server settings for the external LDAP server.", "id": "IdentityServiceServerConfig", "properties": {"certificateAuthorityData": {"description": "Optional. Contains a Base64 encoded, PEM formatted certificate authority certificate for the LDAP server. This must be provided for the \"ldaps\" and \"startTLS\" connections.", "format": "byte", "type": "string"}, "connectionType": {"description": "Optional. Defines the connection type to communicate with the LDAP server. If `starttls` or `ldaps` is specified, the certificate_authority_data should not be empty.", "type": "string"}, "host": {"description": "Required. Defines the hostname or IP of the LDAP server. Port is optional and will default to 389, if unspecified. For example, \"ldap.server.example\" or \"***********:389\".", "type": "string"}}, "type": "object"}, "IdentityServiceServiceAccountConfig": {"description": "Contains the credentials of the service account which is authorized to perform the LDAP search in the directory. The credentials can be supplied by the combination of the DN and password or the client certificate.", "id": "IdentityServiceServiceAccountConfig", "properties": {"simpleBindCredentials": {"$ref": "IdentityServiceSimpleBindCredentials", "description": "Credentials for basic auth."}}, "type": "object"}, "IdentityServiceSimpleBindCredentials": {"description": "The structure holds the LDAP simple binding credential.", "id": "IdentityServiceSimpleBindCredentials", "properties": {"dn": {"description": "Required. The distinguished name(DN) of the service account object/user.", "type": "string"}, "encryptedPassword": {"description": "Output only. The encrypted password of the service account object/user.", "format": "byte", "readOnly": true, "type": "string"}, "password": {"description": "Required. Input only. The password of the service account object/user.", "type": "string"}}, "type": "object"}, "IdentityServiceUserConfig": {"description": "Defines where users exist in the LDAP directory.", "id": "IdentityServiceUserConfig", "properties": {"baseDn": {"description": "Required. The location of the subtree in the LDAP directory to search for user entries.", "type": "string"}, "filter": {"description": "Optional. Filter to apply when searching for the user. This can be used to further restrict the user accounts which are allowed to login. This defaults to \"(objectClass=User)\".", "type": "string"}, "idAttribute": {"description": "Optional. Determines which attribute to use as the user's identity after they are authenticated. This is distinct from the loginAttribute field to allow users to login with a username, but then have their actual identifier be an email address or full Distinguished Name (DN). For example, setting loginAttribute to \"sAMAccount<PERSON><PERSON>\" and identifierAttribute to \"userPrincipalName\" would allow a user to login as \"bsmith\", but actual RBAC policies for the user would be written as \"<EMAIL>\". Using \"userPrincipalName\" is recommended since this will be unique for each user. This defaults to \"userPrincipalName\".", "type": "string"}, "loginAttribute": {"description": "Optional. The name of the attribute which matches against the input username. This is used to find the user in the LDAP database e.g. \"(=)\" and is combined with the optional filter field. This defaults to \"userPrincipalName\".", "type": "string"}}, "type": "object"}, "KubernetesMetadata": {"description": "KubernetesMetadata provides informational metadata for Memberships representing Kubernetes clusters.", "id": "KubernetesMetadata", "properties": {"kubernetesApiServerVersion": {"description": "Output only. Kubernetes API server version string as reported by `/version`.", "readOnly": true, "type": "string"}, "memoryMb": {"description": "Output only. The total memory capacity as reported by the sum of all Kubernetes nodes resources, defined in MB.", "format": "int32", "readOnly": true, "type": "integer"}, "nodeCount": {"description": "Output only. Node count as reported by Kubernetes nodes resources.", "format": "int32", "readOnly": true, "type": "integer"}, "nodeProviderId": {"description": "Output only. Node providerID as reported by the first node in the list of nodes on the Kubernetes endpoint. On Kubernetes platforms that support zero-node clusters (like GKE-on-GCP), the node_count will be zero and the node_provider_id will be empty.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The time at which these details were last updated. This update_time is different from the Membership-level update_time since EndpointDetails are updated internally for API consumers.", "format": "google-datetime", "readOnly": true, "type": "string"}, "vcpuCount": {"description": "Output only. vCPU count as reported by Kubernetes nodes resources.", "format": "int32", "readOnly": true, "type": "integer"}}, "type": "object"}, "KubernetesResource": {"description": "KubernetesResource contains the YAML manifests and configuration for Membership Kubernetes resources in the cluster. After CreateMembership or UpdateMembership, these resources should be re-applied in the cluster.", "id": "KubernetesResource", "properties": {"connectResources": {"description": "Output only. The Kubernetes resources for installing the GKE Connect agent This field is only populated in the Membership returned from a successful long-running operation from CreateMembership or UpdateMembership. It is not populated during normal GetMembership or ListMemberships requests. To get the resource manifest after the initial registration, the caller should make a UpdateMembership call with an empty field mask.", "items": {"$ref": "ResourceManifest"}, "readOnly": true, "type": "array"}, "membershipCrManifest": {"description": "Input only. The YAML representation of the Membership CR. This field is ignored for GKE clusters where <PERSON><PERSON> can read the CR directly. Callers should provide the CR that is currently present in the cluster during CreateMembership or UpdateMembership, or leave this field empty if none exists. The CR manifest is used to validate the cluster has not been registered with another Membership.", "type": "string"}, "membershipResources": {"description": "Output only. Additional Kubernetes resources that need to be applied to the cluster after Membership creation, and after every update. This field is only populated in the Membership returned from a successful long-running operation from CreateMembership or UpdateMembership. It is not populated during normal GetMembership or ListMemberships requests. To get the resource manifest after the initial registration, the caller should make a UpdateMembership call with an empty field mask.", "items": {"$ref": "ResourceManifest"}, "readOnly": true, "type": "array"}, "resourceOptions": {"$ref": "ResourceOptions", "description": "Optional. Options for Kubernetes resource generation."}}, "type": "object"}, "ListBoundMembershipsResponse": {"description": "List of Memberships bound to a Scope.", "id": "ListBoundMembershipsResponse", "properties": {"memberships": {"description": "The list of Memberships bound to the given Scope.", "items": {"$ref": "Membership"}, "type": "array"}, "nextPageToken": {"description": "A token to request the next page of resources from the `ListBoundMemberships` method. The value of an empty string means that there are no more resources to return.", "type": "string"}, "unreachable": {"description": "List of locations that could not be reached while fetching this list.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListFeaturesResponse": {"description": "Response message for the `GkeHub.ListFeatures` method.", "id": "ListFeaturesResponse", "properties": {"nextPageToken": {"description": "A token to request the next page of resources from the `ListFeatures` method. The value of an empty string means that there are no more resources to return.", "type": "string"}, "resources": {"description": "The list of matching Features", "items": {"$ref": "Feature"}, "type": "array"}}, "type": "object"}, "ListFleetsResponse": {"description": "Response message for the `GkeHub.ListFleetsResponse` method.", "id": "ListFleetsResponse", "properties": {"fleets": {"description": "The list of matching fleets.", "items": {"$ref": "Fleet"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages. The token is only valid for 1h.", "type": "string"}}, "type": "object"}, "ListLocationsResponse": {"description": "The response message for Locations.ListLocations.", "id": "ListLocationsResponse", "properties": {"locations": {"description": "A list of locations that matches the specified filter in the request.", "items": {"$ref": "Location"}, "type": "array"}, "nextPageToken": {"description": "The standard List next-page token.", "type": "string"}}, "type": "object"}, "ListMembershipBindingsResponse": {"description": "List of MembershipBindings.", "id": "ListMembershipBindingsResponse", "properties": {"membershipBindings": {"description": "The list of membership_bindings", "items": {"$ref": "MembershipBinding"}, "type": "array"}, "nextPageToken": {"description": "A token to request the next page of resources from the `ListMembershipBindings` method. The value of an empty string means that there are no more resources to return.", "type": "string"}, "unreachable": {"description": "List of locations that could not be reached while fetching this list.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListMembershipRBACRoleBindingsResponse": {"description": "List of Membership RBACRoleBindings.", "id": "ListMembershipRBACRoleBindingsResponse", "properties": {"nextPageToken": {"description": "A token to request the next page of resources from the `ListMembershipRBACRoleBindings` method. The value of an empty string means that there are no more resources to return.", "type": "string"}, "rbacrolebindings": {"description": "The list of Membership RBACRoleBindings.", "items": {"$ref": "RBACRoleBinding"}, "type": "array"}, "unreachable": {"description": "List of locations that could not be reached while fetching this list.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListMembershipsResponse": {"description": "Response message for the `GkeHub.ListMemberships` method.", "id": "ListMembershipsResponse", "properties": {"nextPageToken": {"description": "A token to request the next page of resources from the `ListMemberships` method. The value of an empty string means that there are no more resources to return.", "type": "string"}, "resources": {"description": "The list of matching Memberships.", "items": {"$ref": "Membership"}, "type": "array"}, "unreachable": {"description": "List of locations that could not be reached while fetching this list.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "ListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "Operation"}, "type": "array"}}, "type": "object"}, "ListPermittedScopesResponse": {"description": "List of permitted Scopes.", "id": "ListPermittedScopesResponse", "properties": {"nextPageToken": {"description": "A token to request the next page of resources from the `ListPermittedScopes` method. The value of an empty string means that there are no more resources to return.", "type": "string"}, "scopes": {"description": "The list of permitted Scopes", "items": {"$ref": "<PERSON><PERSON>"}, "type": "array"}}, "type": "object"}, "ListScopeNamespacesResponse": {"description": "List of fleet namespaces.", "id": "ListScopeNamespacesResponse", "properties": {"nextPageToken": {"description": "A token to request the next page of resources from the `ListNamespaces` method. The value of an empty string means that there are no more resources to return.", "type": "string"}, "scopeNamespaces": {"description": "The list of fleet namespaces", "items": {"$ref": "Namespace"}, "type": "array"}}, "type": "object"}, "ListScopeRBACRoleBindingsResponse": {"description": "List of Scope RBACRoleBindings.", "id": "ListScopeRBACRoleBindingsResponse", "properties": {"nextPageToken": {"description": "A token to request the next page of resources from the `ListScopeRBACRoleBindings` method. The value of an empty string means that there are no more resources to return.", "type": "string"}, "rbacrolebindings": {"description": "The list of Scope RBACRoleBindings.", "items": {"$ref": "RBACRoleBinding"}, "type": "array"}}, "type": "object"}, "ListScopesResponse": {"description": "List of Scopes.", "id": "ListScopesResponse", "properties": {"nextPageToken": {"description": "A token to request the next page of resources from the `ListScopes` method. The value of an empty string means that there are no more resources to return.", "type": "string"}, "scopes": {"description": "The list of Scopes", "items": {"$ref": "<PERSON><PERSON>"}, "type": "array"}}, "type": "object"}, "Location": {"description": "A resource that represents a Google Cloud location.", "id": "Location", "properties": {"displayName": {"description": "The friendly name for this location, typically a nearby city name. For example, \"Tokyo\".", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Cross-service attributes for the location. For example {\"cloud.googleapis.com/region\": \"us-east1\"}", "type": "object"}, "locationId": {"description": "The canonical id for this location. For example: `\"us-east1\"`.", "type": "string"}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata. For example the available capacity at the given location.", "type": "object"}, "name": {"description": "Resource name for the location, which may vary between implementations. For example: `\"projects/example-project/locations/us-east1\"`", "type": "string"}}, "type": "object"}, "Membership": {"description": "Membership contains information about a member cluster.", "id": "Membership", "properties": {"authority": {"$ref": "Authority", "description": "Optional. How to identify workloads from this Membership. See the documentation on Workload Identity for more details: https://cloud.google.com/kubernetes-engine/docs/how-to/workload-identity"}, "clusterTier": {"description": "Output only. The tier of the cluster.", "enum": ["CLUSTER_TIER_UNSPECIFIED", "STANDARD", "ENTERPRISE"], "enumDescriptions": ["The ClusterTier is not set.", "The ClusterTier is standard.", "The ClusterTier is enterprise."], "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. When the Membership was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "deleteTime": {"description": "Output only. When the Membership was deleted.", "format": "google-datetime", "readOnly": true, "type": "string"}, "description": {"description": "Output only. Description of this membership, limited to 63 characters. Must match the regex: `a-zA-Z0-9*` This field is present for legacy purposes.", "readOnly": true, "type": "string"}, "endpoint": {"$ref": "MembershipEndpoint", "description": "Optional. Endpoint information to reach this member."}, "externalId": {"description": "Optional. An externally-generated and managed ID for this Membership. This ID may be modified after creation, but this is not recommended. The ID must match the regex: `a-zA-Z0-9*` If this Membership represents a Kubernetes cluster, this value should be set to the UID of the `kube-system` namespace object.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Labels for this membership. These labels are not leveraged by multi-cluster features, instead, we prefer cluster labels, which can be set on GKE cluster or other cluster types.", "type": "object"}, "lastConnectionTime": {"description": "Output only. For clusters using Connect, the timestamp of the most recent connection established with Google Cloud. This time is updated every several minutes, not continuously. For clusters that do not use GKE Connect, or that have never connected successfully, this field will be unset.", "format": "google-datetime", "readOnly": true, "type": "string"}, "monitoringConfig": {"$ref": "MonitoringConfig", "description": "Optional. The monitoring config information for this membership."}, "name": {"description": "Output only. The full, unique name of this Membership resource in the format `projects/*/locations/*/memberships/{membership_id}`, set during creation. `membership_id` must be a valid RFC 1123 compliant DNS label: 1. At most 63 characters in length 2. It must consist of lower case alphanumeric characters or `-` 3. It must start and end with an alphanumeric character Which can be expressed as the regex: `[a-z0-9]([-a-z0-9]*[a-z0-9])?`, with a maximum length of 63 characters.", "readOnly": true, "type": "string"}, "state": {"$ref": "MembershipState", "description": "Output only. State of the Membership resource.", "readOnly": true}, "uniqueId": {"description": "Output only. Google-generated UUID for this resource. This is unique across all Membership resources. If a Membership resource is deleted and another resource with the same name is created, it gets a different unique_id.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. When the Membership was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "MembershipBinding": {"description": "MembershipBinding is a subresource of a Membership, representing what Fleet Scopes (or other, future Fleet resources) a Membership is bound to.", "id": "MembershipBinding", "properties": {"createTime": {"description": "Output only. When the membership binding was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "deleteTime": {"description": "Output only. When the membership binding was deleted.", "format": "google-datetime", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Labels for this MembershipBinding.", "type": "object"}, "name": {"description": "The resource name for the membershipbinding itself `projects/{project}/locations/{location}/memberships/{membership}/bindings/{membershipbinding}`", "type": "string"}, "scope": {"description": "A Scope resource name in the format `projects/*/locations/*/scopes/*`.", "type": "string"}, "state": {"$ref": "MembershipBindingLifecycleState", "description": "Output only. State of the membership binding resource.", "readOnly": true}, "uid": {"description": "Output only. Google-generated UUID for this resource. This is unique across all membershipbinding resources. If a membershipbinding resource is deleted and another resource with the same name is created, it gets a different uid.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. When the membership binding was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "MembershipBindingLifecycleState": {"description": "MembershipBindingLifecycleState describes the state of a Binding resource.", "id": "MembershipBindingLifecycleState", "properties": {"code": {"description": "Output only. The current state of the MembershipBinding resource.", "enum": ["CODE_UNSPECIFIED", "CREATING", "READY", "DELETING", "UPDATING"], "enumDescriptions": ["The code is not set.", "The membershipbinding is being created.", "The membershipbinding active.", "The membershipbinding is being deleted.", "The membershipbinding is being updated."], "readOnly": true, "type": "string"}}, "type": "object"}, "MembershipEndpoint": {"description": "MembershipEndpoint contains information needed to contact a Kubernetes API, endpoint and any additional Kubernetes metadata.", "id": "MembershipEndpoint", "properties": {"applianceCluster": {"$ref": "ApplianceCluster", "description": "Optional. Specific information for a GDC Edge Appliance cluster."}, "edgeCluster": {"$ref": "EdgeCluster", "description": "Optional. Specific information for a Google Edge cluster."}, "gkeCluster": {"$ref": "GkeCluster", "description": "Optional. Specific information for a GKE-on-GCP cluster."}, "googleManaged": {"description": "Output only. Whether the lifecycle of this membership is managed by a google cluster platform service.", "readOnly": true, "type": "boolean"}, "kubernetesMetadata": {"$ref": "KubernetesMetadata", "description": "Output only. Useful Kubernetes-specific metadata.", "readOnly": true}, "kubernetesResource": {"$ref": "KubernetesResource", "description": "Optional. The in-cluster Kubernetes Resources that should be applied for a correctly registered cluster, in the steady state. These resources: * Ensure that the cluster is exclusively registered to one and only one Hub Membership. * Propagate Workload Pool Information available in the Membership Authority field. * Ensure proper initial configuration of default Hub Features."}, "multiCloudCluster": {"$ref": "MultiCloudCluster", "description": "Optional. Specific information for a GKE Multi-Cloud cluster."}, "onPremCluster": {"$ref": "OnPremCluster", "description": "Optional. Specific information for a GKE On-Prem cluster. An onprem user-cluster who has no resourceLink is not allowed to use this field, it should have a nil \"type\" instead."}}, "type": "object"}, "MembershipFeatureSpec": {"description": "MembershipFeatureSpec contains configuration information for a single Membership.", "id": "MembershipFeatureSpec", "properties": {"configmanagement": {"$ref": "ConfigManagementMembershipSpec", "description": "Config Management-specific spec."}, "fleetobservability": {"$ref": "FleetObservabilityMembershipSpec", "description": "Fleet observability membership spec"}, "identityservice": {"$ref": "IdentityServiceMembershipSpec", "description": "Identity Service-specific spec."}, "mesh": {"$ref": "ServiceMeshMembershipSpec", "description": "Anthos Service Mesh-specific spec"}, "origin": {"$ref": "Origin", "description": "Whether this per-Membership spec was inherited from a fleet-level default. This field can be updated by users by either overriding a Membership config (updated to USER implicitly) or setting to FLEET explicitly."}, "policycontroller": {"$ref": "PolicyControllerMembershipSpec", "description": "Policy Controller spec."}}, "type": "object"}, "MembershipFeatureState": {"description": "MembershipFeatureState contains Feature status information for a single Membership.", "id": "MembershipFeatureState", "properties": {"appdevexperience": {"$ref": "AppDevExperienceFeatureState", "description": "Appdevexperience specific state."}, "clusterupgrade": {"$ref": "ClusterUpgradeMembershipState", "description": "ClusterUpgrade state."}, "configmanagement": {"$ref": "ConfigManagementMembershipState", "description": "Config Management-specific state."}, "fleetobservability": {"$ref": "FleetObservabilityMembershipState", "description": "Fleet observability membership state."}, "identityservice": {"$ref": "IdentityServiceMembershipState", "description": "Identity Service-specific state."}, "policycontroller": {"$ref": "PolicyControllerMembershipState", "description": "Policycontroller-specific state."}, "servicemesh": {"$ref": "ServiceMeshMembershipState", "description": "Service Mesh-specific state."}, "state": {"$ref": "FeatureState", "description": "The high-level state of this Feature for a single membership."}}, "type": "object"}, "MembershipState": {"description": "MembershipState describes the state of a Membership resource.", "id": "MembershipState", "properties": {"code": {"description": "Output only. The current state of the Membership resource.", "enum": ["CODE_UNSPECIFIED", "CREATING", "READY", "DELETING", "UPDATING", "SERVICE_UPDATING"], "enumDescriptions": ["The code is not set.", "The cluster is being registered.", "The cluster is registered.", "The cluster is being unregistered.", "The Membership is being updated.", "The Membership is being updated by the Hub Service."], "readOnly": true, "type": "string"}}, "type": "object"}, "MonitoringConfig": {"description": "MonitoringConfig informs Fleet-based applications/services/UIs how the metrics for the underlying cluster is reported to cloud monitoring services. It can be set from empty to non-empty, but can't be mutated directly to prevent accidentally breaking the constinousty of metrics.", "id": "MonitoringConfig", "properties": {"cluster": {"description": "Optional. Cluster name used to report metrics. For Anthos on VMWare/Baremetal/MultiCloud clusters, it would be in format {cluster_type}/{cluster_name}, e.g., \"awsClusters/cluster_1\".", "type": "string"}, "clusterHash": {"description": "Optional. For GKE and Multicloud clusters, this is the UUID of the cluster resource. For VMWare and Baremetal clusters, this is the kube-system UID.", "type": "string"}, "kubernetesMetricsPrefix": {"description": "Optional. Kubernetes system metrics, if available, are written to this prefix. This defaults to kubernetes.io for GKE, and kubernetes.io/anthos for Anthos eventually. Noted: Anthos MultiCloud will have kubernetes.io prefix today but will migration to be under kubernetes.io/anthos.", "type": "string"}, "location": {"description": "Optional. Location used to report Metrics", "type": "string"}, "projectId": {"description": "Optional. Project used to report Metrics", "type": "string"}}, "type": "object"}, "MultiCloudCluster": {"description": "MultiCloudCluster contains information specific to GKE Multi-Cloud clusters.", "id": "MultiCloudCluster", "properties": {"clusterMissing": {"description": "Output only. If cluster_missing is set then it denotes that API(gkemulticloud.googleapis.com) resource for this GKE Multi-Cloud cluster no longer exists.", "readOnly": true, "type": "boolean"}, "resourceLink": {"description": "Immutable. Self-link of the Google Cloud resource for the GKE Multi-Cloud cluster. For example: //gkemulticloud.googleapis.com/projects/my-project/locations/us-west1-a/awsClusters/my-cluster //gkemulticloud.googleapis.com/projects/my-project/locations/us-west1-a/azureClusters/my-cluster //gkemulticloud.googleapis.com/projects/my-project/locations/us-west1-a/attachedClusters/my-cluster", "type": "string"}}, "type": "object"}, "MultiClusterIngressFeatureSpec": {"description": "**Multi-cluster Ingress**: The configuration for the MultiClusterIngress feature.", "id": "MultiClusterIngressFeatureSpec", "properties": {"configMembership": {"description": "Fully-qualified Membership name which hosts the MultiClusterIngress CRD. Example: `projects/foo-proj/locations/global/memberships/bar`", "type": "string"}}, "type": "object"}, "Namespace": {"description": "Namespace represents a namespace across the Fleet", "id": "Namespace", "properties": {"createTime": {"description": "Output only. When the namespace was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "deleteTime": {"description": "Output only. When the namespace was deleted.", "format": "google-datetime", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Labels for this Namespace.", "type": "object"}, "name": {"description": "The resource name for the namespace `projects/{project}/locations/{location}/namespaces/{namespace}`", "type": "string"}, "namespaceLabels": {"additionalProperties": {"type": "string"}, "description": "Optional. Namespace-level cluster namespace labels. These labels are applied to the related namespace of the member clusters bound to the parent Scope. Scope-level labels (`namespace_labels` in the Fleet Scope resource) take precedence over Namespace-level labels if they share a key. Keys and values must be Kubernetes-conformant.", "type": "object"}, "scope": {"description": "Required. <PERSON><PERSON> associated with the namespace", "type": "string"}, "state": {"$ref": "NamespaceLifecycleState", "description": "Output only. State of the namespace resource.", "readOnly": true}, "uid": {"description": "Output only. Google-generated UUID for this resource. This is unique across all namespace resources. If a namespace resource is deleted and another resource with the same name is created, it gets a different uid.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. When the namespace was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "NamespaceLifecycleState": {"description": "NamespaceLifecycleState describes the state of a Namespace resource.", "id": "NamespaceLifecycleState", "properties": {"code": {"description": "Output only. The current state of the Namespace resource.", "enum": ["CODE_UNSPECIFIED", "CREATING", "READY", "DELETING", "UPDATING"], "enumDescriptions": ["The code is not set.", "The namespace is being created.", "The namespace active.", "The namespace is being deleted.", "The namespace is being updated."], "readOnly": true, "type": "string"}}, "type": "object"}, "OnPremCluster": {"description": "OnPremCluster contains information specific to GKE On-Prem clusters.", "id": "OnPremCluster", "properties": {"adminCluster": {"description": "Immutable. Whether the cluster is an admin cluster.", "type": "boolean"}, "clusterMissing": {"description": "Output only. If cluster_missing is set then it denotes that API(gkeonprem.googleapis.com) resource for this GKE On-Prem cluster no longer exists.", "readOnly": true, "type": "boolean"}, "clusterType": {"description": "Immutable. The on prem cluster's type.", "enum": ["CLUSTERTYPE_UNSPECIFIED", "BOOTSTRAP", "HYBRID", "STANDALONE", "USER"], "enumDescriptions": ["The ClusterType is not set.", "The ClusterType is bootstrap cluster.", "The ClusterType is baremetal hybrid cluster.", "The ClusterType is baremetal standalone cluster.", "The ClusterType is user cluster."], "type": "string"}, "resourceLink": {"description": "Immutable. Self-link of the Google Cloud resource for the GKE On-Prem cluster. For example: //gkeonprem.googleapis.com/projects/my-project/locations/us-west1-a/vmwareClusters/my-cluster //gkeonprem.googleapis.com/projects/my-project/locations/us-west1-a/bareMetalClusters/my-cluster", "type": "string"}}, "type": "object"}, "Operation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "Operation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "GoogleRpcStatus", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "OperationMetadata": {"description": "Represents the metadata of the long-running operation.", "id": "OperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "cancelRequested": {"description": "Output only. Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have google.longrunning.Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "statusDetail": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "Origin": {"description": "Origin defines where this MembershipFeatureSpec originated from.", "id": "Origin", "properties": {"type": {"description": "Type specifies which type of origin is set.", "enum": ["TYPE_UNSPECIFIED", "FLEET", "FLEET_OUT_OF_SYNC", "USER"], "enumDescriptions": ["Type is unknown or not set.", "Per-Membership spec was inherited from the fleet-level default.", "Per-Membership spec was inherited from the fleet-level default but is now out of sync with the current default.", "Per-Membership spec was inherited from a user specification."], "type": "string"}}, "type": "object"}, "Policy": {"description": "An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { \"bindings\": [ { \"role\": \"roles/resourcemanager.organizationAdmin\", \"members\": [ \"user:<EMAIL>\", \"group:<EMAIL>\", \"domain:google.com\", \"serviceAccount:<EMAIL>\" ] }, { \"role\": \"roles/resourcemanager.organizationViewer\", \"members\": [ \"user:<EMAIL>\" ], \"condition\": { \"title\": \"expirable access\", \"description\": \"Does not grant access after Sep 2020\", \"expression\": \"request.time < timestamp('2020-10-01T00:00:00.000Z')\", } } ], \"etag\": \"BwWWja0YfJA=\", \"version\": 3 } ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).", "id": "Policy", "properties": {"auditConfigs": {"description": "Specifies cloud audit logging configuration for this policy.", "items": {"$ref": "AuditConfig"}, "type": "array"}, "bindings": {"description": "Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.", "items": {"$ref": "Binding"}, "type": "array"}, "etag": {"description": "`etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.", "format": "byte", "type": "string"}, "version": {"description": "Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "PolicyBinding": {"description": "Binauthz policy that applies to this cluster.", "id": "PolicyBinding", "properties": {"name": {"description": "The relative resource name of the binauthz platform policy to audit. GKE platform policies have the following format: `projects/{project_number}/platforms/gke/policies/{policy_id}`.", "type": "string"}}, "type": "object"}, "PolicyControllerBundleInstallSpec": {"description": "BundleInstallSpec is the specification configuration for a single managed bundle.", "id": "PolicyControllerBundleInstallSpec", "properties": {"exemptedNamespaces": {"description": "The set of namespaces to be exempted from the bundle.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "PolicyControllerHubConfig": {"description": "Configuration for Policy Controller", "id": "PolicyControllerHubConfig", "properties": {"auditIntervalSeconds": {"description": "Sets the interval for Policy Controller <PERSON><PERSON> (in seconds). When set to 0, this disables audit functionality altogether.", "format": "int64", "type": "string"}, "constraintViolationLimit": {"description": "The maximum number of audit violations to be stored in a constraint. If not set, the internal default (currently 20) will be used.", "format": "int64", "type": "string"}, "deploymentConfigs": {"additionalProperties": {"$ref": "PolicyControllerPolicyControllerDeploymentConfig"}, "description": "Map of deployment configs to deployments (\"admission\", \"audit\", \"mutation').", "type": "object"}, "exemptableNamespaces": {"description": "The set of namespaces that are excluded from Policy Controller checks. Namespaces do not need to currently exist on the cluster.", "items": {"type": "string"}, "type": "array"}, "installSpec": {"description": "The install_spec represents the intended state specified by the latest request that mutated install_spec in the feature spec, not the lifecycle state of the feature observed by the Hub feature controller that is reported in the feature state.", "enum": ["INSTALL_SPEC_UNSPECIFIED", "INSTALL_SPEC_NOT_INSTALLED", "INSTALL_SPEC_ENABLED", "INSTALL_SPEC_SUSPENDED", "INSTALL_SPEC_DETACHED"], "enumDescriptions": ["Spec is unknown.", "Request to uninstall Policy Controller.", "Request to install and enable Policy Controller.", "Request to suspend Policy Controller i.e. its webhooks. If Policy Controller is not installed, it will be installed but suspended.", "Request to stop all reconciliation actions by PoCo Hub controller. This is a breakglass mechanism to stop PoCo Hub from affecting cluster resources."], "type": "string"}, "logDeniesEnabled": {"description": "Logs all denies and dry run failures.", "type": "boolean"}, "monitoring": {"$ref": "PolicyControllerMonitoringConfig", "description": "Monitoring specifies the configuration of monitoring."}, "mutationEnabled": {"description": "Enables the ability to mutate resources using Policy Controller.", "type": "boolean"}, "policyContent": {"$ref": "PolicyControllerPolicyContentSpec", "description": "Specifies the desired policy content on the cluster"}, "referentialRulesEnabled": {"description": "Enables the ability to use Constraint Templates that reference to objects other than the object currently being evaluated.", "type": "boolean"}}, "type": "object"}, "PolicyControllerMembershipSpec": {"description": "**Policy Controller**: Configuration for a single cluster. Intended to parallel the PolicyController CR.", "id": "PolicyControllerMembershipSpec", "properties": {"policyControllerHubConfig": {"$ref": "PolicyControllerHubConfig", "description": "Policy Controller configuration for the cluster."}, "version": {"description": "Version of Policy Controller installed.", "type": "string"}}, "type": "object"}, "PolicyControllerMembershipState": {"description": "**Policy Controller**: State for a single cluster.", "id": "PolicyControllerMembershipState", "properties": {"componentStates": {"additionalProperties": {"$ref": "PolicyControllerOnClusterState"}, "description": "Currently these include (also serving as map keys): 1. \"admission\" 2. \"audit\" 3. \"mutation\"", "type": "object"}, "policyContentState": {"$ref": "PolicyControllerPolicyContentState", "description": "The overall content state observed by the Hub Feature controller."}, "state": {"description": "The overall Policy Controller lifecycle state observed by the Hub Feature controller.", "enum": ["LIFECYCLE_STATE_UNSPECIFIED", "NOT_INSTALLED", "INSTALLING", "ACTIVE", "UPDATING", "DECOMMISSIONING", "CLUSTER_ERROR", "HUB_ERROR", "SUSPENDED", "DETACHED"], "enumDescriptions": ["The lifecycle state is unspecified.", "The PC does not exist on the given cluster, and no k8s resources of any type that are associated with the PC should exist there. The cluster does not possess a membership with the PCH.", "The PCH possesses a Membership, however the PC is not fully installed on the cluster. In this state the hub can be expected to be taking actions to install the PC on the cluster.", "The PC is fully installed on the cluster and in an operational mode. In this state PCH will be reconciling state with the PC, and the PC will be performing it's operational tasks per that software. Entering a READY state requires that the hub has confirmed the PC is installed and its pods are operational with the version of the PC the PCH expects.", "The PC is fully installed, but in the process of changing the configuration (including changing the version of PC either up and down, or modifying the manifests of PC) of the resources running on the cluster. The PCH has a Membership, is aware of the version the cluster should be running in, but has not confirmed for itself that the PC is running with that version.", "The PC may have resources on the cluster, but the PCH wishes to remove the Membership. The Membership still exists.", "The PC is not operational, and the PCH is unable to act to make it operational. Entering a CLUSTER_ERROR state happens automatically when the PCH determines that a PC installed on the cluster is non-operative or that the cluster does not meet requirements set for the PCH to administer the cluster but has nevertheless been given an instruction to do so (such as 'install').", "In this state, the PC may still be operational, and only the PCH is unable to act. The hub should not issue instructions to change the PC state, or otherwise interfere with the on-cluster resources. Entering a HUB_ERROR state happens automatically when the PCH determines the hub is in an unhealthy state and it wishes to 'take hands off' to avoid corrupting the PC or other data.", "Policy Controller (PC) is installed but suspended. This means that the policies are not enforced, but violations are still recorded (through audit).", "PoCo Hub is not taking any action to reconcile cluster objects. Changes to those objects will not be overwritten by PoCo Hub."], "type": "string"}}, "type": "object"}, "PolicyControllerMonitoringConfig": {"description": "MonitoringConfig specifies the backends Policy Controller should export metrics to. For example, to specify metrics should be exported to Cloud Monitoring and Prometheus, specify backends: [\"cloudmonitoring\", \"prometheus\"]", "id": "PolicyControllerMonitoringConfig", "properties": {"backends": {"description": "Specifies the list of backends Policy Controller will export to. An empty list would effectively disable metrics export.", "items": {"enum": ["MONITORING_BACKEND_UNSPECIFIED", "PROMETHEUS", "CLOUD_MONITORING"], "enumDescriptions": ["Backend cannot be determined", "Prometheus backend for monitoring", "Stackdriver/Cloud Monitoring backend for monitoring"], "type": "string"}, "type": "array"}}, "type": "object"}, "PolicyControllerOnClusterState": {"description": "OnClusterState represents the state of a sub-component of Policy Controller.", "id": "PolicyControllerOnClusterState", "properties": {"details": {"description": "Surface potential errors or information logs.", "type": "string"}, "state": {"description": "The lifecycle state of this component.", "enum": ["LIFECYCLE_STATE_UNSPECIFIED", "NOT_INSTALLED", "INSTALLING", "ACTIVE", "UPDATING", "DECOMMISSIONING", "CLUSTER_ERROR", "HUB_ERROR", "SUSPENDED", "DETACHED"], "enumDescriptions": ["The lifecycle state is unspecified.", "The PC does not exist on the given cluster, and no k8s resources of any type that are associated with the PC should exist there. The cluster does not possess a membership with the PCH.", "The PCH possesses a Membership, however the PC is not fully installed on the cluster. In this state the hub can be expected to be taking actions to install the PC on the cluster.", "The PC is fully installed on the cluster and in an operational mode. In this state PCH will be reconciling state with the PC, and the PC will be performing it's operational tasks per that software. Entering a READY state requires that the hub has confirmed the PC is installed and its pods are operational with the version of the PC the PCH expects.", "The PC is fully installed, but in the process of changing the configuration (including changing the version of PC either up and down, or modifying the manifests of PC) of the resources running on the cluster. The PCH has a Membership, is aware of the version the cluster should be running in, but has not confirmed for itself that the PC is running with that version.", "The PC may have resources on the cluster, but the PCH wishes to remove the Membership. The Membership still exists.", "The PC is not operational, and the PCH is unable to act to make it operational. Entering a CLUSTER_ERROR state happens automatically when the PCH determines that a PC installed on the cluster is non-operative or that the cluster does not meet requirements set for the PCH to administer the cluster but has nevertheless been given an instruction to do so (such as 'install').", "In this state, the PC may still be operational, and only the PCH is unable to act. The hub should not issue instructions to change the PC state, or otherwise interfere with the on-cluster resources. Entering a HUB_ERROR state happens automatically when the PCH determines the hub is in an unhealthy state and it wishes to 'take hands off' to avoid corrupting the PC or other data.", "Policy Controller (PC) is installed but suspended. This means that the policies are not enforced, but violations are still recorded (through audit).", "PoCo Hub is not taking any action to reconcile cluster objects. Changes to those objects will not be overwritten by PoCo Hub."], "type": "string"}}, "type": "object"}, "PolicyControllerPolicyContentSpec": {"description": "PolicyContentSpec defines the user's desired content configuration on the cluster.", "id": "PolicyControllerPolicyContentSpec", "properties": {"bundles": {"additionalProperties": {"$ref": "PolicyControllerBundleInstallSpec"}, "description": "map of bundle name to BundleInstallSpec. The bundle name maps to the `bundleName` key in the `policycontroller.gke.io/constraintData` annotation on a constraint.", "type": "object"}, "templateLibrary": {"$ref": "PolicyControllerTemplateLibraryConfig", "description": "Configures the installation of the Template Library."}}, "type": "object"}, "PolicyControllerPolicyContentState": {"description": "The state of the policy controller policy content", "id": "PolicyControllerPolicyContentState", "properties": {"bundleStates": {"additionalProperties": {"$ref": "PolicyControllerOnClusterState"}, "description": "The state of the any bundles included in the chosen version of the manifest", "type": "object"}, "referentialSyncConfigState": {"$ref": "PolicyControllerOnClusterState", "description": "The state of the referential data sync configuration. This could represent the state of either the syncSet object(s) or the config object, depending on the version of PoCo configured by the user."}, "templateLibraryState": {"$ref": "PolicyControllerOnClusterState", "description": "The state of the template library"}}, "type": "object"}, "PolicyControllerPolicyControllerDeploymentConfig": {"description": "Deployment-specific configuration.", "id": "PolicyControllerPolicyControllerDeploymentConfig", "properties": {"containerResources": {"$ref": "PolicyControllerResourceRequirements", "description": "Container resource requirements."}, "podAffinity": {"description": "Pod affinity configuration.", "enum": ["AFFINITY_UNSPECIFIED", "NO_AFFINITY", "ANTI_AFFINITY"], "enumDescriptions": ["No affinity configuration has been specified.", "Affinity configurations will be removed from the deployment.", "Anti-affinity configuration will be applied to this deployment. Default for admissions deployment."], "type": "string"}, "podAntiAffinity": {"deprecated": true, "description": "Pod anti-affinity enablement. Deprecated: use `pod_affinity` instead.", "type": "boolean"}, "podTolerations": {"description": "Pod tolerations of node taints.", "items": {"$ref": "PolicyControllerToleration"}, "type": "array"}, "replicaCount": {"description": "Pod replica count.", "format": "int64", "type": "string"}}, "type": "object"}, "PolicyControllerResourceList": {"description": "ResourceList contains container resource requirements.", "id": "PolicyControllerResourceList", "properties": {"cpu": {"description": "CPU requirement expressed in Kubernetes resource units.", "type": "string"}, "memory": {"description": "Memory requirement expressed in Kubernetes resource units.", "type": "string"}}, "type": "object"}, "PolicyControllerResourceRequirements": {"description": "ResourceRequirements describes the compute resource requirements.", "id": "PolicyControllerResourceRequirements", "properties": {"limits": {"$ref": "PolicyControllerResourceList", "description": "Limits describes the maximum amount of compute resources allowed for use by the running container."}, "requests": {"$ref": "PolicyControllerResourceList", "description": "Requests describes the amount of compute resources reserved for the container by the kube-scheduler."}}, "type": "object"}, "PolicyControllerTemplateLibraryConfig": {"description": "The config specifying which default library templates to install.", "id": "PolicyControllerTemplateLibraryConfig", "properties": {"installation": {"description": "Configures the manner in which the template library is installed on the cluster.", "enum": ["INSTALLATION_UNSPECIFIED", "NOT_INSTALLED", "ALL"], "enumDescriptions": ["No installation strategy has been specified.", "Do not install the template library.", "Install the entire template library."], "type": "string"}}, "type": "object"}, "PolicyControllerToleration": {"description": "Toleration of a node taint.", "id": "PolicyControllerToleration", "properties": {"effect": {"description": "Matches a taint effect.", "type": "string"}, "key": {"description": "Matches a taint key (not necessarily unique).", "type": "string"}, "operator": {"description": "Matches a taint operator.", "type": "string"}, "value": {"description": "Matches a taint value.", "type": "string"}}, "type": "object"}, "RBACRoleBinding": {"description": "RBACRoleBinding represents a rbacrolebinding across the Fleet", "id": "RBACRoleBinding", "properties": {"createTime": {"description": "Output only. When the rbacrolebinding was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "deleteTime": {"description": "Output only. When the rbacrolebinding was deleted.", "format": "google-datetime", "readOnly": true, "type": "string"}, "group": {"description": "group is the group, as seen by the kubernetes cluster.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Labels for this RBACRolebinding.", "type": "object"}, "name": {"description": "The resource name for the rbacrolebinding `projects/{project}/locations/{location}/scopes/{scope}/rbacrolebindings/{rbacrolebinding}` or `projects/{project}/locations/{location}/memberships/{membership}/rbacrolebindings/{rbacrolebinding}`", "type": "string"}, "role": {"$ref": "Role", "description": "Required. Role to bind to the principal"}, "state": {"$ref": "RBACRoleBindingLifecycleState", "description": "Output only. State of the rbacrolebinding resource.", "readOnly": true}, "uid": {"description": "Output only. Google-generated UUID for this resource. This is unique across all rbacrolebinding resources. If a rbacrolebinding resource is deleted and another resource with the same name is created, it gets a different uid.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. When the rbacrolebinding was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}, "user": {"description": "user is the name of the user as seen by the kubernetes cluster, example \"alice\" or \"<EMAIL>\"", "type": "string"}}, "type": "object"}, "RBACRoleBindingActuationFeatureSpec": {"description": "**RBAC RoleBinding Actuation**: The Hub-wide input for the RBACRoleBindingActuation feature.", "id": "RBACRoleBindingActuationFeatureSpec", "properties": {"allowedCustomRoles": {"description": "The list of allowed custom roles (ClusterRoles). If a ClusterRole is not part of this list, it cannot be used in a Scope RBACRoleBinding. If a ClusterRole in this list is in use, it cannot be removed from the list.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "RBACRoleBindingActuationFeatureState": {"description": "**RBAC RoleBinding Actuation**: An empty state left as an example Hub-wide Feature state.", "id": "RBACRoleBindingActuationFeatureState", "properties": {}, "type": "object"}, "RBACRoleBindingLifecycleState": {"description": "RBACRoleBindingLifecycleState describes the state of a RbacRoleBinding resource.", "id": "RBACRoleBindingLifecycleState", "properties": {"code": {"description": "Output only. The current state of the rbacrolebinding resource.", "enum": ["CODE_UNSPECIFIED", "CREATING", "READY", "DELETING", "UPDATING"], "enumDescriptions": ["The code is not set.", "The rbacrolebinding is being created.", "The rbacrolebinding active.", "The rbacrolebinding is being deleted.", "The rbacrolebinding is being updated."], "readOnly": true, "type": "string"}}, "type": "object"}, "ResourceManifest": {"description": "ResourceManifest represents a single Kubernetes resource to be applied to the cluster.", "id": "ResourceManifest", "properties": {"clusterScoped": {"description": "Output only. Whether the resource provided in the manifest is `cluster_scoped`. If unset, the manifest is assumed to be namespace scoped. This field is used for REST mapping when applying the resource in a cluster.", "readOnly": true, "type": "boolean"}, "manifest": {"description": "Output only. YAML manifest of the resource.", "readOnly": true, "type": "string"}}, "type": "object"}, "ResourceOptions": {"description": "ResourceOptions represent options for Kubernetes resource generation.", "id": "ResourceOptions", "properties": {"connectVersion": {"description": "Optional. The Connect agent version to use for connect_resources. Defaults to the latest GKE Connect version. The version must be a currently supported version, obsolete versions will be rejected.", "type": "string"}, "k8sVersion": {"description": "Optional. Major version of the Kubernetes cluster. This is only used to determine which version to use for the CustomResourceDefinition resources, `apiextensions/v1beta1` or`apiextensions/v1`.", "type": "string"}, "v1beta1Crd": {"description": "Optional. Use `apiextensions/v1beta1` instead of `apiextensions/v1` for CustomResourceDefinition resources. This option should be set for clusters with Kubernetes apiserver versions <1.16.", "type": "boolean"}}, "type": "object"}, "Role": {"description": "Role is the type for <PERSON><PERSON><PERSON><PERSON> roles", "id": "Role", "properties": {"customRole": {"description": "Optional. custom_role is the name of a custom KubernetesClusterRole to use.", "type": "string"}, "predefinedRole": {"description": "predefined_role is the Kubernetes default role to use", "enum": ["UNKNOWN", "ADMIN", "EDIT", "VIEW", "ANTHOS_SUPPORT"], "enumDescriptions": ["UNKNOWN", "ADMIN has EDIT and RBAC permissions", "EDIT can edit all resources except RBAC", "VIEW can only read resources", "ANTHOS_SUPPORT gives Google Support read-only access to a number of cluster resources."], "type": "string"}}, "type": "object"}, "Scope": {"description": "<PERSON><PERSON> represents a <PERSON><PERSON> in a Fleet.", "id": "<PERSON><PERSON>", "properties": {"createTime": {"description": "Output only. When the scope was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "deleteTime": {"description": "Output only. When the scope was deleted.", "format": "google-datetime", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Labels for this Scope.", "type": "object"}, "name": {"description": "The resource name for the scope `projects/{project}/locations/{location}/scopes/{scope}`", "type": "string"}, "namespaceLabels": {"additionalProperties": {"type": "string"}, "description": "Optional. Scope-level cluster namespace labels. For the member clusters bound to the Scope, these labels are applied to each namespace under the Scope. Scope-level labels take precedence over Namespace-level labels (`namespace_labels` in the Fleet Namespace resource) if they share a key. Keys and values must be Kubernetes-conformant.", "type": "object"}, "state": {"$ref": "ScopeLifecycleState", "description": "Output only. State of the scope resource.", "readOnly": true}, "uid": {"description": "Output only. Google-generated UUID for this resource. This is unique across all scope resources. If a scope resource is deleted and another resource with the same name is created, it gets a different uid.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. When the scope was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "ScopeFeatureSpec": {"description": "ScopeFeatureSpec contains feature specs for a fleet scope.", "id": "ScopeFeatureSpec", "properties": {}, "type": "object"}, "ScopeFeatureState": {"description": "ScopeFeatureState contains Scope-wide Feature status information.", "id": "ScopeFeatureState", "properties": {"state": {"$ref": "FeatureState", "description": "Output only. The \"running state\" of the Feature in this Scope.", "readOnly": true}}, "type": "object"}, "ScopeLifecycleState": {"description": "ScopeLifecycleState describes the state of a Scope resource.", "id": "ScopeLifecycleState", "properties": {"code": {"description": "Output only. The current state of the scope resource.", "enum": ["CODE_UNSPECIFIED", "CREATING", "READY", "DELETING", "UPDATING"], "enumDescriptions": ["The code is not set.", "The scope is being created.", "The scope active.", "The scope is being deleted.", "The scope is being updated."], "readOnly": true, "type": "string"}}, "type": "object"}, "SecurityPostureConfig": {"description": "SecurityPostureConfig defines the flags needed to enable/disable features for the Security Posture API.", "id": "SecurityPostureConfig", "properties": {"mode": {"description": "Sets which mode to use for Security Posture features.", "enum": ["MODE_UNSPECIFIED", "DISABLED", "BASIC", "ENTERPRISE"], "enumDescriptions": ["Default value not specified.", "Disables Security Posture features on the cluster.", "Applies Security Posture features on the cluster.", "Applies the Security Posture off cluster Enterprise level features."], "type": "string"}, "vulnerabilityMode": {"description": "Sets which mode to use for vulnerability scanning.", "enum": ["VULNERABILITY_MODE_UNSPECIFIED", "VULNERABILITY_DISABLED", "VULNERABILITY_BASIC", "VULNERABILITY_ENTERPRISE"], "enumDescriptions": ["Default value not specified.", "Disables vulnerability scanning on the cluster.", "Applies basic vulnerability scanning on the cluster.", "Applies the Security Posture's vulnerability on cluster Enterprise level features."], "type": "string"}}, "type": "object"}, "ServiceMeshCondition": {"description": "Condition being reported.", "id": "ServiceMeshCondition", "properties": {"code": {"description": "Unique identifier of the condition which describes the condition recognizable to the user.", "enum": ["CODE_UNSPECIFIED", "MESH_IAM_PERMISSION_DENIED", "MESH_IAM_CROSS_PROJECT_PERMISSION_DENIED", "CNI_CONFIG_UNSUPPORTED", "GKE_SANDBOX_UNSUPPORTED", "NODEPOOL_WORKLOAD_IDENTITY_FEDERATION_REQUIRED", "CNI_INSTALLATION_FAILED", "CNI_POD_UNSCHEDULABLE", "CLUSTER_HAS_ZERO_NODES", "CANONICAL_SERVICE_ERROR", "UNSUPPORTED_MULTIPLE_CONTROL_PLANES", "VPCSC_GA_SUPPORTED", "DEPRECATED_SPEC_CONTROL_PLANE_MANAGEMENT", "DEPRECATED_SPEC_CONTROL_PLANE_MANAGEMENT_SAFE", "CONFIG_APPLY_INTERNAL_ERROR", "CONFIG_VALIDATION_ERROR", "CONFIG_VALIDATION_WARNING", "QUOTA_EXCEEDED_BACKEND_SERVICES", "QUOTA_EXCEEDED_HEALTH_CHECKS", "QUOTA_EXCEEDED_HTTP_ROUTES", "QUOTA_EXCEEDED_TCP_ROUTES", "QUOTA_EXCEEDED_TLS_ROUTES", "QUOTA_EXCEEDED_TRAFFIC_POLICIES", "QUOTA_EXCEEDED_ENDPOINT_POLICIES", "QUOTA_EXCEEDED_GATEWAYS", "QUOTA_EXCEEDED_MESHES", "QUOTA_EXCEEDED_SERVER_TLS_POLICIES", "QUOTA_EXCEEDED_CLIENT_TLS_POLICIES", "QUOTA_EXCEEDED_SERVICE_LB_POLICIES", "QUOTA_EXCEEDED_HTTP_FILTERS", "QUOTA_EXCEEDED_TCP_FILTERS", "QUOTA_EXCEEDED_NETWORK_ENDPOINT_GROUPS", "LEGACY_MC_SECRETS", "WORKLOAD_IDENTITY_REQUIRED", "NON_STANDARD_BINARY_USAGE", "UNSUPPORTED_GATEWAY_CLASS", "MANAGED_CNI_NOT_ENABLED", "MODERNIZATION_SCHEDULED", "MODERNIZATION_IN_PROGRESS", "MODERNIZATION_COMPLETED", "MODERNIZATION_ABORTED", "MODERNIZATION_WILL_BE_SCHEDULED"], "enumDescriptions": ["Default Unspecified code", "Mesh IAM permission denied error code", "Permission denied error code for cross-project", "CNI config unsupported error code", "GKE sandbox unsupported error code", "Nodepool workload identity federation required error code", "CNI installation failed error code", "CNI pod unschedulable error code", "Cluster has zero node code", "Failure to reconcile CanonicalServices", "Multiple control planes unsupported error code", "VPC-SC GA is supported for this control plane.", "User is using deprecated ControlPlaneManagement and they have not yet set Management.", "User is using deprecated ControlPlaneManagement and they have already set Management.", "Configuration (Istio/k8s resources) failed to apply due to internal error.", "Configuration failed to be applied due to being invalid.", "Encountered configuration(s) with possible unintended behavior or invalid configuration. These configs may not have been applied.", "BackendService quota exceeded error code.", "HealthCheck quota exceeded error code.", "HTTPRoute quota exceeded error code.", "TCPRoute quota exceeded error code.", "TLS routes quota exceeded error code.", "TrafficPolicy quota exceeded error code.", "EndpointPolicy quota exceeded error code.", "Gateway quota exceeded error code.", "Mesh quota exceeded error code.", "ServerTLSPolicy quota exceeded error code.", "ClientTLSPolicy quota exceeded error code.", "ServiceLBPolicy quota exceeded error code.", "HTTPFilter quota exceeded error code.", "TCPFilter quota exceeded error code.", "NetworkEndpointGroup quota exceeded error code.", "Legacy istio secrets found for multicluster error code", "Workload identity required error code", "Non-standard binary usage error code", "Unsupported gateway class error code", "Managed CNI not enabled error code", "Modernization is scheduled for a cluster.", "Modernization is in progress for a cluster.", "Modernization is completed for a cluster.", "Modernization is aborted for a cluster.", "Modernization will be scheduled for a fleet."], "type": "string"}, "details": {"description": "A short summary about the issue.", "type": "string"}, "documentationLink": {"description": "Links contains actionable information.", "type": "string"}, "severity": {"description": "Severity level of the condition.", "enum": ["SEVERITY_UNSPECIFIED", "ERROR", "WARNING", "INFO"], "enumDescriptions": ["Unspecified severity", "Indicates an issue that prevents the mesh from operating correctly", "Indicates a setting is likely wrong, but the mesh is still able to operate", "An informational message, not requiring any action"], "type": "string"}}, "type": "object"}, "ServiceMeshControlPlaneManagement": {"description": "Status of control plane management.", "id": "ServiceMeshControlPlaneManagement", "properties": {"details": {"description": "Explanation of state.", "items": {"$ref": "ServiceMeshStatusDetails"}, "type": "array"}, "implementation": {"description": "Output only. Implementation of managed control plane.", "enum": ["IMPLEMENTATION_UNSPECIFIED", "ISTIOD", "TRAFFIC_DIRECTOR", "UPDATING"], "enumDescriptions": ["Unspecified", "A Google build of istiod is used for the managed control plane.", "Traffic director is used for the managed control plane.", "The control plane implementation is being updated."], "readOnly": true, "type": "string"}, "state": {"description": "LifecycleState of control plane management.", "enum": ["LIFECYCLE_STATE_UNSPECIFIED", "DISABLED", "FAILED_PRECONDITION", "PROVISIONING", "ACTIVE", "STALLED", "NEEDS_ATTENTION", "DEGRADED"], "enumDescriptions": ["Unspecified", "DISABLED means that the component is not enabled.", "FAILED_PRECONDITION means that provisioning cannot proceed because of some characteristic of the member cluster.", "PROVISIONING means that provisioning is in progress.", "ACTIVE means that the component is ready for use.", "STALLED means that provisioning could not be done.", "NEEDS_ATTENTION means that the component is ready, but some user intervention is required. (For example that the user should migrate workloads to a new control plane revision.)", "DEGRADED means that the component is ready, but operating in a degraded state."], "type": "string"}}, "type": "object"}, "ServiceMeshDataPlaneManagement": {"description": "Status of data plane management. Only reported per-member.", "id": "ServiceMeshDataPlaneManagement", "properties": {"details": {"description": "Explanation of the status.", "items": {"$ref": "ServiceMeshStatusDetails"}, "type": "array"}, "state": {"description": "Lifecycle status of data plane management.", "enum": ["LIFECYCLE_STATE_UNSPECIFIED", "DISABLED", "FAILED_PRECONDITION", "PROVISIONING", "ACTIVE", "STALLED", "NEEDS_ATTENTION", "DEGRADED"], "enumDescriptions": ["Unspecified", "DISABLED means that the component is not enabled.", "FAILED_PRECONDITION means that provisioning cannot proceed because of some characteristic of the member cluster.", "PROVISIONING means that provisioning is in progress.", "ACTIVE means that the component is ready for use.", "STALLED means that provisioning could not be done.", "NEEDS_ATTENTION means that the component is ready, but some user intervention is required. (For example that the user should migrate workloads to a new control plane revision.)", "DEGRADED means that the component is ready, but operating in a degraded state."], "type": "string"}}, "type": "object"}, "ServiceMeshMembershipSpec": {"description": "**Service Mesh**: Spec for a single Membership for the servicemesh feature", "id": "ServiceMeshMembershipSpec", "properties": {"configApi": {"description": "Optional. Specifies the API that will be used for configuring the mesh workloads.", "enum": ["CONFIG_API_UNSPECIFIED", "CONFIG_API_ISTIO", "CONFIG_API_GATEWAY"], "enumDescriptions": ["Unspecified", "Use the Istio API for configuration.", "Use the K8s Gateway API for configuration."], "type": "string"}, "controlPlane": {"deprecated": true, "description": "Deprecated: use `management` instead Enables automatic control plane management.", "enum": ["CONTROL_PLANE_MANAGEMENT_UNSPECIFIED", "AUTOMATIC", "MANUAL"], "enumDescriptions": ["Unspecified", "Google should provision a control plane revision and make it available in the cluster. Google will enroll this revision in a release channel and keep it up to date. The control plane revision may be a managed service, or a managed install.", "User will manually configure the control plane (e.g. via CLI, or via the ControlPlaneRevision KRM API)"], "type": "string"}, "management": {"description": "Optional. Enables automatic Service Mesh management.", "enum": ["MANAGEMENT_UNSPECIFIED", "MANAGEMENT_AUTOMATIC", "MANAGEMENT_MANUAL"], "enumDescriptions": ["Unspecified", "Google should manage my Service Mesh for the cluster.", "User will manually configure their service mesh components."], "type": "string"}}, "type": "object"}, "ServiceMeshMembershipState": {"description": "**Service Mesh**: State for a single Membership, as analyzed by the Service Mesh Hub Controller.", "id": "ServiceMeshMembershipState", "properties": {"conditions": {"description": "Output only. List of conditions reported for this membership.", "items": {"$ref": "ServiceMeshCondition"}, "readOnly": true, "type": "array"}, "controlPlaneManagement": {"$ref": "ServiceMeshControlPlaneManagement", "description": "Output only. Status of control plane management", "readOnly": true}, "dataPlaneManagement": {"$ref": "ServiceMeshDataPlaneManagement", "description": "Output only. Status of data plane management.", "readOnly": true}}, "type": "object"}, "ServiceMeshStatusDetails": {"description": "Structured and human-readable details for a status.", "id": "ServiceMeshStatusDetails", "properties": {"code": {"description": "A machine-readable code that further describes a broad status.", "type": "string"}, "details": {"description": "Human-readable explanation of code.", "type": "string"}}, "type": "object"}, "SetIamPolicyRequest": {"description": "Request message for `SetIamPolicy` method.", "id": "SetIamPolicyRequest", "properties": {"policy": {"$ref": "Policy", "description": "REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them."}, "updateMask": {"description": "OPTIONAL: A FieldMask specifying which fields of the policy to modify. Only the fields in the mask will be modified. If no mask is provided, the following default mask is used: `paths: \"bindings, etag\"`", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "Status": {"description": "Status specifies state for the subcomponent.", "id": "Status", "properties": {"code": {"description": "Code specifies AppDevExperienceFeature's subcomponent ready state.", "enum": ["CODE_UNSPECIFIED", "OK", "FAILED", "UNKNOWN"], "enumDescriptions": ["Not set.", "AppDevExperienceFeature's specified subcomponent is ready.", "AppDevExperienceFeature's specified subcomponent ready state is false. This means AppDevExperienceFeature has encountered an issue that blocks all, or a portion, of its normal operation. See the `description` for more details.", "AppDevExperienceFeature's specified subcomponent has a pending or unknown state."], "type": "string"}, "description": {"description": "Description is populated if Code is Failed, explaining why it has failed.", "type": "string"}}, "type": "object"}, "TestIamPermissionsRequest": {"description": "Request message for `TestIamPermissions` method.", "id": "TestIamPermissionsRequest", "properties": {"permissions": {"description": "The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TestIamPermissionsResponse": {"description": "Response message for `TestIamPermissions` method.", "id": "TestIamPermissionsResponse", "properties": {"permissions": {"description": "A subset of `TestPermissionsRequest.permissions` that the caller is allowed.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TypeMeta": {"description": "TypeMeta is the type information needed for content unmarshalling of Kubernetes resources in the manifest.", "id": "TypeMeta", "properties": {"apiVersion": {"description": "APIVersion of the resource (e.g. v1).", "type": "string"}, "kind": {"description": "Kind of the resource (e.g. Deployment).", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "GKE Hub API", "version": "v1", "version_module": true}